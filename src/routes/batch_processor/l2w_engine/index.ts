/**
 * L2W (Lynx-to-Web) Engine
 * 
 * Entry point for the new high-fidelity rendering engine.
 * This engine will be responsible for parsing TTML/TTSS and rendering it in a web environment.
 */

import { transform } from './transformer';
import { initializeApi } from './api';

export class L2WEngine {
  private container: HTMLElement;

  constructor(container: HTMLElement) {
    this.container = container;
    // Initialize the API simulation layer as soon as the engine is instantiated.
    initializeApi();
    console.log('L2W Engine Initialized.');
  }

  public render(ttml: string, ttss: string, data: Record<string, any>) {
    try {
      const webComponent = transform(ttml, ttss, data);
      this.container.innerHTML = ''; // Clear previous content
      this.container.appendChild(webComponent);
      console.log('Render successful.');
    } catch (error) {
      console.error('L2W Engine Render Error:', error);
      this.container.innerHTML = `<pre style="color: red;">${(error as Error).message}</pre>`;
    }
  }
}