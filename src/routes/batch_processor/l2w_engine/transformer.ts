/**
 * L2W (Lynx-to-Web) Transformer
 * 
 * This file contains the core logic for transforming TTML and TTSS into a web-compatible DOM tree.
 * It will implement the rules defined in the 'Lynx与Web语法映射对比升级方案.md'.
 */

import { parse, L2WNode } from './parser';
import { applyStyles } from './styler';
import { bindData } from './binder';
import { attachEventListener } from './event';

const COMPONENT_MAP: Record<string, string> = {
  view: 'div',
  text: 'span',
  image: 'img',
  'scroll-view': 'div',
  // ... add more mappings from the document
};

export function transform(ttml: string, ttss: string, data: Record<string, any>): HTMLElement {
  console.log('Transformation process started.');

  // 1. Parse TTML into our L2W AST
  const ast = parse(ttml);

  // 2. Transform the L2W AST to an HTML DOM tree, binding data in the process
  const domTree = transformNode(ast, data);

  const rootElement = (domTree.firstChild as HTMLElement) || document.createElement('div');

  // 3. Parse TTSS and apply styles
  applyStyles(ttss, rootElement);

  console.log('Transformation process finished.');
  return rootElement;
}

/**
 * Recursively transforms an L2WNode and its children into a DOM tree.
 * @param node The L2WNode to transform.
 * @returns A DocumentFragment containing the resulting DOM structure.
 */
function transformNode(node: L2WNode, data: Record<string, any>): DocumentFragment {
  const fragment = document.createDocumentFragment();

  // Process the node itself
  if (node.type === 'element' && node.tagName) {
    // Root node is a container, we only process its children
    if (node.tagName === 'root') {
      node.children.forEach(child => {
        fragment.appendChild(transformNode(child, data));
      });
      return fragment;
    }

    const htmlTag = COMPONENT_MAP[node.tagName] || 'div'; // Fallback to div
    const element = document.createElement(htmlTag);

    // Apply attributes and attach event listeners
    for (const [key, value] of Object.entries(node.attributes)) {
      if (key.startsWith('bind') || key.startsWith('catch:')) {
        attachEventListener(element, key, value);
      } else {
        element.setAttribute(key, bindData(value, data));
      }
    }

    // Recursively transform and append children
    if (node.children) {
      node.children.forEach(child => {
        element.appendChild(transformNode(child, data));
      });
    }
    fragment.appendChild(element);

  } else if (node.type === 'text') {
    // Bind data to text content
    const boundContent = bindData(node.content || '', data);
    const textNode = document.createTextNode(boundContent);
    fragment.appendChild(textNode);
  }

  return fragment;
}