/**
 * L2W (Lynx-to-Web) API Simulation Layer
 * 
 * This file simulates the native Lynx API environment for the web preview.
 * It creates a global `lynx` object with methods that mirror the native behavior.
 */

// Define the structure of the SystemInfo object
interface SystemInfo {
  brand: string;
  model: string;
  pixelRatio: number;
  screenWidth: number;
  screenHeight: number;
  windowWidth: number;
  windowHeight: number;
  statusBarHeight: number;
  language: string;
  version: string;
  system: string;
  platform: string;
  fontSizeSetting: number;
  SDKVersion: string;
}

// Define the structure of the global lynx object
interface LynxAPI {
  getSystemInfoSync(): SystemInfo;
  request(options: any): Promise<any>;
  navigateTo(options: any): void;
  // ... other APIs to be added
}

// The simulated SystemInfo, based on browser environment
const systemInfo: SystemInfo = {
  brand: 'L2W-Engine',
  model: 'Browser',
  pixelRatio: window.devicePixelRatio,
  screenWidth: window.screen.width,
  screenHeight: window.screen.height,
  windowWidth: window.innerWidth,
  windowHeight: window.innerHeight,
  statusBarHeight: 20, // Mock value
  language: navigator.language,
  version: '1.0.0', // L2W engine version
  system: navigator.platform,
  platform: 'web',
  fontSizeSetting: 16, // Mock value
  SDKVersion: '1.0.0', // Mock SDK version
};

// The implementation of the simulated Lynx API
const lynx: LynxAPI = {
  getSystemInfoSync() {
    return systemInfo;
  },

  async request(options: any) {
    // Simulate lynx.request using the Fetch API
    const { url, data, header, method, success, fail } = options;
    try {
      const response = await fetch(url, {
        method: method || 'GET',
        headers: header || {},
        body: JSON.stringify(data),
      });
      const responseData = await response.json();
      if (success) {
        success({ data: responseData, statusCode: response.status, header: response.headers });
      }
      return { data: responseData, statusCode: response.status, header: response.headers };
    } catch (error) {
      if (fail) {
        fail(error);
      }
      throw error;
    }
  },

  navigateTo(options: any) {
    // Simulate lynx.navigateTo using the History API or by changing window.location
    const { url } = options;
    console.log(`Navigating to: ${url}`);
    window.location.href = url;
  },
};

/**
 * Initializes the Lynx API simulation layer by attaching the `lynx` object to the window.
 */
export function initializeApi() {
  if (!(window as any).lynx) {
    (window as any).lynx = lynx;
    console.log('Lynx API simulation layer initialized.');
  }
}