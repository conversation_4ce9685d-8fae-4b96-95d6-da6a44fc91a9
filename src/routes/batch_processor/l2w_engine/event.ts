/**
 * L2W (Lynx-to-Web) Event Handler
 * 
 * This file is responsible for mapping Lynx events (e.g., bindtap) to web events
 * and attaching the appropriate listeners to DOM elements.
 */

// Mapping from Lynx events to standard web events
const EVENT_MAP: Record<string, string> = {
  tap: 'click',
  // ... add other event mappings as needed
};

/**
 * Attaches an event listener to a DOM element based on a Lynx event attribute.
 * 
 * @param element The HTMLElement to attach the listener to.
 * @param attributeName The Lynx event attribute name (e.g., 'bindtap').
 * @param handlerName The name of the handler function to call.
 */
export function attachEventListener(element: HTMLElement, attributeName: string, handlerName: string) {
  const [type, eventName] = attributeName.split(':');

  if (!eventName) return;

  const webEventName = EVENT_MAP[eventName];
  if (!webEventName) {
    console.warn(`Unsupported event type: ${eventName}`);
    return;
  }

  // The actual handler function is expected to be globally available
  // on a specific object for the web preview environment.
  const handler = (window as any).lynxEventHandlers?.[handlerName];

  if (typeof handler === 'function') {
    element.addEventListener(webEventName, (event) => {
      // Prevent default behavior for 'catch' events
      if (type === 'catch') {
        event.preventDefault();
      }
      
      // Simulate a simplified Lynx event object
      const lynxEvent = {
        type: eventName,
        target: {
          id: element.id,
          dataset: element.dataset,
        },
        currentTarget: {
            id: element.id,
            dataset: element.dataset,
        },
        detail: {},
      };

      handler(lynxEvent);
    });
  } else {
    console.warn(`Event handler not found: ${handlerName}`);
  }
}