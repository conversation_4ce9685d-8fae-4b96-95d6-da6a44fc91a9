import React, { useState, useEffect, useRef } from 'react';

// -----------------------------------------------------------------------------
// StatusLogger 组件
// -----------------------------------------------------------------------------
// 该组件显示系统操作日志，包含时间戳和消息内容
// 支持不同日志级别，自动滚动到最新记录
// -----------------------------------------------------------------------------

export type LogLevel = 'info' | 'success' | 'warning' | 'error';

export interface LogEntry {
  id: string;
  timestamp: number;
  message: string;
  level: LogLevel;
}

interface StatusLoggerProps {
  /** 日志条目数组 */
  logs: LogEntry[];
  /** 最大显示数量，超出时将移除最早的日志 */
  maxDisplayCount?: number;
}

export const StatusLogger: React.FC<StatusLoggerProps> = ({
  logs,
  maxDisplayCount = 50,
}) => {
  // 日志容器引用，用于自动滚动
  const logContainerRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (logContainerRef.current) {
      const { scrollHeight } = logContainerRef.current;
      logContainerRef.current.scrollTop = scrollHeight;
    }
  }, [logs]);

  // 如果日志条目过多，只显示最新的 maxDisplayCount 条
  const displayLogs = logs.slice(-maxDisplayCount);

  // 格式化时间戳
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 根据日志级别获取样式类
  const getLevelClass = (level: LogLevel): string => {
    switch (level) {
      case 'success':
        return 'log-level-success';
      case 'warning':
        return 'log-level-warning';
      case 'error':
        return 'log-level-error';
      default:
        return 'log-level-info';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 flex flex-col h-full">
      <h3 className="text-sm font-medium text-gray-700 mb-2">系统日志</h3>

      <div
        ref={logContainerRef}
        className="bg-gray-50 border border-gray-200 rounded p-2 flex-1 overflow-y-auto text-xs"
      >
        {displayLogs.length === 0 ? (
          <div className="text-gray-400 italic p-2">暂无日志记录</div>
        ) : (
          displayLogs.map(log => (
            <div
              key={log.id}
              className="py-1 border-b border-gray-100 last:border-0"
            >
              <span className="text-gray-500">
                [{formatTimestamp(log.timestamp)}]
              </span>{' '}
              <span className={getLevelClass(log.level)}>{log.message}</span>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default StatusLogger;
