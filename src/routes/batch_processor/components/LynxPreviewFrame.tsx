/**
 * Lynx预览框架组件
 * 集成Lynx到Web转换器，提供实时预览功能
 * 支持iframe隔离、错误处理、性能监控
 */

import {
  Alert,
  Button,
  Modal,
  Select,
  Space,
  Spin,
  Switch,
  Tag,
  Tooltip,
} from '@arco-design/web-react';
import {
  IconBug,
  IconCode,
  IconDesktop,
  IconFullscreen,
  IconPhone,
  IconRefresh,
  IconSettings,
  IconTablet,
  IconThunderbolt,
} from '@arco-design/web-react/icon';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  DEFAULT_PARSE5_CONFIG,
  Parse5EnhancedConverter,
  type Parse5ConversionConfig,
} from '../utils/parse5EnhancedConverter';
import './LynxPreviewFrame.module.scss';

export interface LynxPreviewFrameProps {
  ttml?: string;
  ttss?: string;
  js?: string;
  visible?: boolean;
  onClose?: () => void;
  className?: string;
  autoRefresh?: boolean;
  enableDevTools?: boolean;
}

interface ConversionResult {
  html: string;
  css: string;
  javascript: string;
  success: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    elementsConverted: number;
    stylesConverted: number;
    eventsConverted: number;
    duration: number;
  };
}

interface PreviewDevice {
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
  userAgent?: string;
}

const PREVIEW_DEVICES: PreviewDevice[] = [
  { name: '手机', width: 375, height: 667, icon: <IconPhone /> },
  { name: '平板', width: 768, height: 1024, icon: <IconTablet /> },
  { name: '桌面', width: 1200, height: 800, icon: <IconDesktop /> },
];

const LynxPreviewFrame: React.FC<LynxPreviewFrameProps> = ({
  ttml = '',
  ttss = '',
  js = '',
  visible = true,
  onClose,
  className = '',
  autoRefresh = true,
  enableDevTools = true,
}) => {
  // 状态管理
  const [conversionResult, setConversionResult] =
    useState<ConversionResult | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionError, setConversionError] = useState<string>('');
  const [currentDevice, setCurrentDevice] = useState<PreviewDevice>(
    PREVIEW_DEVICES[0],
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [parse5Config, setParse5Config] = useState<Parse5ConversionConfig>(
    DEFAULT_PARSE5_CONFIG,
  );

  // Refs
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const conversionTimeoutRef = useRef<NodeJS.Timeout>();

  /**
   * 执行Lynx转换 (Parse5增强器)
   */
  const performConversion = useCallback(async () => {
    if (!ttml && !ttss && !js) {
      setConversionResult(null);
      return;
    }

    setIsConverting(true);
    setConversionError('');

    try {
      // 使用Parse5增强转换器
      const parse5Converter = new Parse5EnhancedConverter(parse5Config);
      const parse5Result = await parse5Converter.convert(
        ttml || '',
        ttss || '',
        js || '',
      );

      const result: ConversionResult = {
        html: parse5Result.html,
        css: parse5Result.css,
        javascript: parse5Result.javascript,
        success: parse5Result.success,
        errors: parse5Result.errors.map(e => e.message),
        warnings: parse5Result.warnings.map(w => w.message),
        stats: {
          elementsConverted: parse5Result.statistics.elementsProcessed,
          stylesConverted: parse5Result.statistics.stylesProcessed,
          eventsConverted: parse5Result.statistics.eventsProcessed,
          duration: parse5Result.statistics.conversionTime,
        },
      };

      setConversionResult(result);

      if (!result.success) {
        setConversionError(result.errors.join('; '));
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '转换失败';
      setConversionError(errorMessage);
      setConversionResult(null);
    } finally {
      setIsConverting(false);
    }
  }, [ttml, ttss, js, parse5Config]);

  /**
   * 防抖转换
   */
  const debouncedConversion = useCallback(() => {
    if (conversionTimeoutRef.current) {
      clearTimeout(conversionTimeoutRef.current);
    }

    conversionTimeoutRef.current = setTimeout(performConversion, 500);
  }, [performConversion]);

  /**
   * 自动刷新效果
   */
  useEffect(() => {
    if (autoRefresh && visible) {
      debouncedConversion();
    }

    return () => {
      if (conversionTimeoutRef.current) {
        clearTimeout(conversionTimeoutRef.current);
      }
    };
  }, [ttml, ttss, js, autoRefresh, visible, debouncedConversion]);

  /**
   * 更新iframe内容
   */
  useEffect(() => {
    if (conversionResult?.success && iframeRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;

      if (doc) {
        doc.open();
        doc.write(conversionResult.html);
        doc.close();

        // 添加错误监听
        if (iframe.contentWindow) {
          iframe.contentWindow.addEventListener('error', e => {
            console.warn('Preview iframe error:', e.error);
          });
        }
      }
    }
  }, [conversionResult]);

  /**
   * 手动刷新
   */
  const handleRefresh = useCallback(() => {
    performConversion();
  }, [performConversion]);

  /**
   * 切换设备
   */
  const handleDeviceChange = useCallback((device: PreviewDevice) => {
    setCurrentDevice(device);
  }, []);

  /**
   * 切换全屏
   */
  const handleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  /**
   * 更新Parse5配置
   */
  const handleConfigChange = useCallback(
    (newConfig: Partial<Parse5ConversionConfig>) => {
      setParse5Config(prev => ({ ...prev, ...newConfig }));
    },
    [],
  );

  /**
   * 计算iframe样式
   */
  const iframeStyle = useMemo(
    () => ({
      width: isFullscreen ? '100%' : `${currentDevice.width}px`,
      height: isFullscreen ? '100%' : `${currentDevice.height}px`,
      border: '1px solid #e5e6eb',
      borderRadius: '8px',
      transition: 'all 0.3s ease',
    }),
    [currentDevice, isFullscreen],
  );

  /**
   * 渲染转换统计
   */
  const renderStats = () => {
    if (!conversionResult?.stats) return null;

    const { stats } = conversionResult;

    return (
      <Space size="small">
        <Tag color="blue">元素: {stats.elementsConverted}</Tag>
        <Tag color="green">样式: {stats.stylesConverted}</Tag>
        <Tag color="orange">事件: {stats.eventsConverted}</Tag>
        <Tag color="purple">{stats.duration}ms</Tag>
      </Space>
    );
  };

  /**
   * 渲染设备选择器
   */
  const renderDeviceSelector = () => (
    <Space>
      {PREVIEW_DEVICES.map(device => (
        <Tooltip key={device.name} content={`${device.width}x${device.height}`}>
          <Button
            type={currentDevice.name === device.name ? 'primary' : 'outline'}
            icon={device.icon}
            size="small"
            onClick={() => handleDeviceChange(device)}
          >
            {device.name}
          </Button>
        </Tooltip>
      ))}
    </Space>
  );

  /**
   * 渲染设置面板
   */
  const renderSettingsPanel = () => (
    <Modal
      title="Parse5转换器配置"
      visible={showSettings}
      onCancel={() => setShowSettings(false)}
      footer={null}
      width={500}
    >
      <div className="settings-panel">
        {/* 基础配置 */}
        <div className="setting-section">
          <h4>
            <IconThunderbolt style={{ marginRight: '8px' }} />
            基础配置
          </h4>
          <div className="setting-item">
            <label>RPX模式:</label>
            <Select
              value={parse5Config.rpxMode}
              onChange={value => handleConfigChange({ rpxMode: value as any })}
              style={{ width: 200 }}
            >
              <Select.Option value="vw">VW模式 (推荐)</Select.Option>
              <Select.Option value="rem">REM模式</Select.Option>
              <Select.Option value="px">PX模式</Select.Option>
              <Select.Option value="calc">CALC模式</Select.Option>
            </Select>
          </div>

          <div className="setting-item">
            <label>设计稿宽度:</label>
            <Select
              value={parse5Config.baseWidth}
              onChange={value => handleConfigChange({ baseWidth: value })}
              style={{ width: 200 }}
            >
              <Select.Option value={750}>750px (标准)</Select.Option>
              <Select.Option value={375}>375px (小屏)</Select.Option>
              <Select.Option value={1200}>1200px (桌面)</Select.Option>
            </Select>
          </div>

          <div className="setting-item">
            <label>目标平台:</label>
            <Select
              value={parse5Config.targetPlatform}
              onChange={value =>
                handleConfigChange({ targetPlatform: value as any })
              }
              style={{ width: 200 }}
            >
              <Select.Option value="web">Web平台</Select.Option>
              <Select.Option value="hybrid">混合平台</Select.Option>
              <Select.Option value="native">原生平台</Select.Option>
            </Select>
          </div>
        </div>

        {/* 优化配置 */}
        <div className="setting-section">
          <h4>性能优化</h4>
          <div className="setting-item">
            <label>Tree Shaking:</label>
            <Switch
              checked={parse5Config.enableTreeShaking}
              onChange={checked =>
                handleConfigChange({ enableTreeShaking: checked })
              }
            />
            <span className="setting-tip">移除未使用的代码</span>
          </div>

          <div className="setting-item">
            <label>样式内联:</label>
            <Switch
              checked={parse5Config.enableStyleInlining}
              onChange={checked =>
                handleConfigChange({ enableStyleInlining: checked })
              }
            />
            <span className="setting-tip">提升首屏性能</span>
          </div>

          <div className="setting-item">
            <label>组件作用域:</label>
            <Switch
              checked={parse5Config.enableComponentScoping}
              onChange={checked =>
                handleConfigChange({ enableComponentScoping: checked })
              }
            />
            <span className="setting-tip">避免样式冲突</span>
          </div>

          <div className="setting-item">
            <label>Hover类转换:</label>
            <Switch
              checked={parse5Config.enableHoverClassTransform}
              onChange={checked =>
                handleConfigChange({ enableHoverClassTransform: checked })
              }
            />
          </div>

          <div className="setting-item">
            <label>启用缓存:</label>
            <Switch
              checked={parse5Config.enableCache}
              onChange={checked => handleConfigChange({ enableCache: checked })}
            />
            <span className="setting-tip">提升重复转换性能</span>
          </div>
        </div>

        {/* 调试配置 */}
        <div className="setting-section">
          <h4>调试工具</h4>
          <div className="setting-item">
            <label>保留注释:</label>
            <Switch
              checked={parse5Config.preserveComments}
              onChange={checked =>
                handleConfigChange({ preserveComments: checked })
              }
            />
          </div>

          <div className="setting-item">
            <label>生成Source Map:</label>
            <Switch
              checked={parse5Config.generateSourceMap}
              onChange={checked =>
                handleConfigChange({ generateSourceMap: checked })
              }
            />
            <span className="setting-tip">便于调试</span>
          </div>

          <div className="setting-item">
            <label>调试模式:</label>
            <Switch
              checked={parse5Config.enableDebugMode}
              onChange={checked =>
                handleConfigChange({ enableDebugMode: checked })
              }
            />
            <span className="setting-tip">详细错误信息</span>
          </div>
        </div>
      </div>
    </Modal>
  );

  if (!visible) return null;

  return (
    <div
      ref={containerRef}
      className={`lynx-preview-frame ${className} ${isFullscreen ? 'fullscreen' : ''}`}
    >
      {/* 工具栏 */}
      <div className="preview-toolbar">
        <div className="toolbar-left">
          <Space>
            <Button
              type="outline"
              icon={<IconRefresh />}
              loading={isConverting}
              onClick={handleRefresh}
              size="small"
            >
              刷新
            </Button>

            {enableDevTools && (
              <Button
                type="outline"
                icon={<IconSettings />}
                onClick={() => setShowSettings(true)}
                size="small"
              >
                设置
              </Button>
            )}

            <Button
              type="outline"
              icon={<IconFullscreen />}
              onClick={handleFullscreen}
              size="small"
            >
              {isFullscreen ? '退出全屏' : '全屏'}
            </Button>
          </Space>
        </div>

        <div className="toolbar-center">
          {!isFullscreen && renderDeviceSelector()}
        </div>

        <div className="toolbar-right">{renderStats()}</div>
      </div>

      {/* 错误显示 */}
      {conversionError && (
        <Alert
          type="error"
          message="转换错误"
          description={conversionError}
          showIcon
          closable
          onClose={() => setConversionError('')}
          style={{ margin: '8px 0' }}
        />
      )}

      {/* 警告显示 */}
      {conversionResult?.warnings && conversionResult.warnings.length > 0 && (
        <Alert
          type="warning"
          message="转换警告"
          description={conversionResult.warnings.join('; ')}
          showIcon
          closable
          style={{ margin: '8px 0' }}
        />
      )}

      {/* 预览区域 */}
      <div className="preview-container">
        {isConverting && (
          <div className="loading-overlay">
            <Spin size="large" tip="转换中..." />
          </div>
        )}

        {!isConverting && !ttml && !ttss && !js && (
          <div className="empty-state">
            <IconCode style={{ fontSize: '48px', color: '#c9cdd4' }} />
            <p>请输入Lynx代码进行预览</p>
          </div>
        )}

        {!isConverting && conversionResult?.success && (
          <iframe
            ref={iframeRef}
            title="Lynx Preview"
            style={iframeStyle}
            sandbox="allow-scripts allow-same-origin allow-forms"
            className="preview-iframe"
          />
        )}

        {!isConverting && conversionResult && !conversionResult.success && (
          <div className="error-state">
            <IconBug style={{ fontSize: '48px', color: '#f53f3f' }} />
            <p>转换失败，请检查代码语法</p>
            {conversionResult.errors.map((error, index) => (
              <p key={index} className="error-detail">
                {error}
              </p>
            ))}
          </div>
        )}
      </div>

      {/* 设置面板 */}
      {renderSettingsPanel()}
    </div>
  );
};

export default LynxPreviewFrame;
