import React from 'react';

interface DonutChartProps {
  data: {
    label: string;
    value: number;
    color: string;
    bgColor: string;
  }[];
  size?: number;
  strokeWidth?: number;
  showPercentage?: boolean;
}

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  size = 120,
  strokeWidth = 12,
  showPercentage = true,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // 计算总值
  const total = data.reduce((sum, item) => sum + item.value, 0);

  // 如果没有数据，显示空状态
  if (total === 0) {
    return (
      <div className="flex flex-col items-center">
        <div className="relative" style={{ width: size, height: size }}>
          <svg width={size} height={size}>
            <circle
              cx={center}
              cy={center}
              r={radius}
              fill="transparent"
              stroke="#f3f4f6"
              strokeWidth={strokeWidth}
            />
          </svg>
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-lg font-bold text-gray-400">0</div>
            <div className="text-xs text-gray-400">暂无数据</div>
          </div>
        </div>
        <div className="mt-3 space-y-1.5 w-full">
          {data.map((item, index) => (
            <div
              key={index}
              className="flex items-center justify-between text-xs"
            >
              <div className="flex items-center">
                <div
                  className="w-2.5 h-2.5 rounded-full mr-2 shadow-sm"
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-gray-700">{item.label}</span>
              </div>
              <span className="font-medium text-gray-800">0</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 简化的圆环图实现 - 只显示一个完整的圆环，用颜色表示主要状态
  const primaryData = data.find(item => item.value > 0) || data[0];
  const primaryColor = primaryData?.color || '#3b82f6';

  // 计算进度百分比（如果有成功的，显示成功进度；否则显示等待进度）
  const successData = data.find(item => item.label === '成功');
  // const waitingData = data.find(item => item.label === '等待中'); // Unused
  const progressPercentage =
    successData && successData.value > 0
      ? (successData.value / total) * 100
      : 0;

  const progressStrokeLength = (progressPercentage / 100) * circumference;

  return (
    <div className="flex flex-col items-center">
      {/* 圆环图 */}
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="transform -rotate-90">
          {/* 背景圆环 */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="transparent"
            stroke="#f3f4f6"
            strokeWidth={strokeWidth}
          />

          {/* 主要状态圆环 - 显示等待中状态 */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="transparent"
            stroke={primaryColor}
            strokeWidth={strokeWidth}
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={0}
            strokeLinecap="round"
            className="transition-all duration-500 ease-in-out"
            style={{
              filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))',
              opacity: 0.8,
            }}
          />

          {/* 进度圆环 - 显示成功进度 */}
          {progressPercentage > 0 && (
            <circle
              cx={center}
              cy={center}
              r={radius}
              fill="transparent"
              stroke="#87ceeb"
              strokeWidth={strokeWidth}
              strokeDasharray={`${progressStrokeLength} ${circumference}`}
              strokeDashoffset={0}
              strokeLinecap="round"
              className="transition-all duration-500 ease-in-out"
              style={{
                filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))',
              }}
            />
          )}
        </svg>

        {/* 中心文字 */}
        {showPercentage && (
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-lg font-bold text-gray-800">{total}</div>
            <div className="text-xs text-gray-500">总计</div>
          </div>
        )}
      </div>

      {/* 图例 */}
      <div className="mt-3 space-y-1.5 w-full">
        {data.map((item, index) => (
          <div
            key={index}
            className="flex items-center justify-between text-xs"
          >
            <div className="flex items-center">
              <div
                className="w-2.5 h-2.5 rounded-full mr-2 shadow-sm"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-gray-700">{item.label}</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="font-medium text-gray-800">{item.value}</span>
              {total > 0 && (
                <span className="text-xs text-gray-500">
                  ({((item.value / total) * 100).toFixed(0)}%)
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DonutChart;
