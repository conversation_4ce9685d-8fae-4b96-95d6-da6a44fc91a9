/**
 * Lynx预览框架样式
 * 响应式设计，支持多设备预览和全屏模式
 */

.lynx-preview-frame {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    border-radius: 0;
  }
}

/* 工具栏样式 */
.preview-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: none;
  min-height: 48px;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    display: flex;
    align-items: center;
  }
  
  .toolbar-center {
    flex: 1;
    justify-content: center;
  }
  
  .toolbar-right {
    justify-content: flex-end;
    min-width: 200px;
  }
}

/* 预览容器 */
.preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #f5f5f5;
  padding: 20px;
  overflow: auto;
  
  .fullscreen & {
    padding: 0;
  }
}

/* iframe预览 */
.preview-iframe {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  border-radius: 12px;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
  
  .fullscreen & {
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #86909c;
  
  p {
    margin-top: 16px;
    font-size: 16px;
  }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #f53f3f;
  max-width: 500px;
  
  p {
    margin-top: 16px;
    font-size: 16px;
    
    &.error-detail {
      font-size: 14px;
      color: #86909c;
      margin-top: 8px;
      padding: 8px 12px;
      background: #fff2f0;
      border-radius: 4px;
      border-left: 3px solid #f53f3f;
    }
  }
}

/* 设置面板 */
.settings-panel {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
  
  .setting-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: none;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    h4 {
      display: flex;
      align-items: center;
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
      
      :global(.arco-icon) {
        color: #3b82f6;
      }
    }
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    label {
      font-weight: 500;
      color: #1d2129;
      min-width: 120px;
      font-size: 14px;
    }
    
    .setting-tip {
      font-size: 12px;
      color: #86909c;
      margin-left: 8px;
      font-style: italic;
    }
  }
  
  /* 设置面板滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 3px;
    
    &:hover {
      background: #a0a0a0;
    }
  }
}

/* 设备切换动画 */
@keyframes deviceSwitch {
  0% {
    transform: scale(0.9);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.preview-iframe[data-device-switching="true"] {
  animation: deviceSwitch 0.3s ease-out;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .preview-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
      width: 100%;
      justify-content: center;
    }
    
    .toolbar-right {
      min-width: auto;
    }
  }
  
  .preview-container {
    padding: 12px;
  }
  
  .preview-iframe {
    max-width: 100%;
    height: auto;
  }
}

/* 全屏模式下的特殊样式 */
.lynx-preview-frame.fullscreen {
  .preview-toolbar {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    
    :global(.arco-btn) {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    
    :global(.arco-tag) {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
  
  .preview-container {
    background: #000;
  }
}

/* 转换统计标签 */
:global(.arco-tag) {
  &[data-color="blue"] {
    background: #e8f4ff;
    color: #165dff;
    border-color: #bedaff;
  }
  
  &[data-color="green"] {
    background: #e8ffea;
    color: #00b42a;
    border-color: #aed5b1;
  }
  
  &[data-color="orange"] {
    background: #fff7e8;
    color: #ff7d00;
    border-color: #ffcb9a;
  }
  
  &[data-color="purple"] {
    background: #f5e8ff;
    color: #722ed1;
    border-color: #d1b3ff;
  }
}

/* 滚动条优化 */
.preview-container {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 4px;
    
    &:hover {
      background: #a0a0a0;
    }
  }
}

/* 优化动画性能 */
.preview-iframe,
.preview-container,
.lynx-preview-frame {
  will-change: transform;
  transform: translateZ(0);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .lynx-preview-frame {
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .preview-toolbar {
    background: #2a2a2a;
    border-bottom-color: #404040;
    color: #ffffff;
  }
  
  .preview-container {
    background: #262626;
  }
  
  .empty-state,
  .error-state {
    .error-detail {
      background: #2a1a1a;
      color: #cccccc;
    }
  }
}