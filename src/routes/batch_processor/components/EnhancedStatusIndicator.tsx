import React from 'react';
import Icon from './Icon';

// ═══════════════════════════════════════════════════════════════════════════
// 🎯 ENHANCED STATUS INDICATOR - 强化状态指示器组件
// ═══════════════════════════════════════════════════════════════════════════

export type StatusType = 'success' | 'error' | 'processing' | 'pending';
export type StatusSize = 'sm' | 'md' | 'lg' | 'xl';

interface EnhancedStatusIndicatorProps {
  /** 状态类型 */
  status: StatusType;
  /** 指示器尺寸 */
  size?: StatusSize;
  /** 是否显示状态文本 */
  showText?: boolean;
  /** 自定义状态文本 */
  customText?: string;
  /** 是否显示为徽章样式 */
  badge?: boolean;
  /** 点击回调 */
  onClick?: () => void;
  /** 额外的CSS类名 */
  className?: string;
  /** 工具提示内容 */
  tooltip?: string;
}

const statusConfig = {
  success: {
    icon: 'check',
    text: '成功',
    color: 'success' as const,
  },
  error: {
    icon: 'error',
    text: '失败',
    color: 'error' as const,
  },
  processing: {
    icon: 'processing',
    text: '处理中',
    color: 'processing' as const,
  },
  pending: {
    icon: 'clock',
    text: '等待',
    color: 'neutral' as const,
  },
};

export const EnhancedStatusIndicator: React.FC<
  EnhancedStatusIndicatorProps
> = ({
  status,
  size = 'md',
  showText = false,
  customText,
  badge = false,
  onClick,
  className = '',
  tooltip,
}) => {
  const config = statusConfig[status];
  const displayText = customText || config.text;

  if (badge) {
    return (
      <div
        className={`status-badge status-badge--${status} ${className}`}
        onClick={onClick}
        title={tooltip}
        style={{ cursor: onClick ? 'pointer' : 'default' }}
      >
        <div
          className={`status-indicator status-indicator--${status} status-indicator--sm`}
        >
          <Icon
            type={config.icon as any}
            color="white"
            size="sm"
            animate={status === 'processing'}
          />
        </div>
        <span>{displayText}</span>
      </div>
    );
  }

  const indicatorElement = (
    <div
      className={`status-indicator status-indicator--${status} status-indicator--${size} ${className}`}
      onClick={onClick}
      title={tooltip}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <Icon
        type={config.icon as any}
        color="white"
        size={size === 'sm' ? 'sm' : size === 'lg' ? 'md' : 'sm'}
        animate={status === 'processing'}
      />
    </div>
  );

  if (showText) {
    return (
      <div className="flex items-center gap-2">
        {indicatorElement}
        <span className="text-sm font-medium text-gray-700">{displayText}</span>
      </div>
    );
  }

  return indicatorElement;
};

// ═══════════════════════════════════════════════════════════════════════════
// 📊 STATUS OVERVIEW - 状态概览组件
// ═══════════════════════════════════════════════════════════════════════════

interface StatusOverviewProps {
  /** 总数 */
  total: number;
  /** 成功数量 */
  success: number;
  /** 失败数量 */
  error: number;
  /** 处理中数量 */
  processing: number;
  /** 等待数量 */
  pending?: number;
  /** 是否显示详细信息 */
  detailed?: boolean;
  /** 点击状态的回调 */
  onStatusClick?: (status: StatusType) => void;
}

export const StatusOverview: React.FC<StatusOverviewProps> = ({
  total,
  success,
  error,
  processing,
  pending,
  detailed = false,
  onStatusClick,
}) => {
  const pendingCount = pending ?? total - success - error - processing;

  const stats = [
    { status: 'success' as StatusType, count: success, label: '成功' },
    { status: 'error' as StatusType, count: error, label: '失败' },
    { status: 'processing' as StatusType, count: processing, label: '处理中' },
    { status: 'pending' as StatusType, count: pendingCount, label: '等待' },
  ].filter(stat => stat.count > 0);

  if (detailed) {
    // Calculate grid columns based on total items (1 total + filtered stats)
    const totalItems = 1 + stats.length;
    const gridCols = totalItems <= 2 ? 'grid-cols-2' : 
                     totalItems <= 3 ? 'grid-cols-3' : 
                     totalItems <= 4 ? 'grid-cols-2 lg:grid-cols-4' : 
                     'grid-cols-2 lg:grid-cols-5';
    
    return (
      <div className={`grid gap-3 ${gridCols}`}>
        <div className="status-overview-item bg-blue-50 border border-blue-200 rounded-lg p-3 text-center h-20 flex flex-col justify-center">
          <div className="text-2xl font-bold text-blue-600">{total}</div>
          <div className="text-xs text-gray-600 mt-1">总数</div>
        </div>
        {stats.map(({ status, count, label }) => (
          <div
            key={status}
            className={`status-overview-item bg-gray-50 border rounded-lg p-3 text-center transition-all duration-200 h-20 flex flex-col justify-center ${
              onStatusClick ? 'cursor-pointer hover:bg-gray-100' : ''
            }`}
            onClick={() => onStatusClick?.(status)}
          >
            <div className="flex items-center justify-center mb-2">
              <EnhancedStatusIndicator status={status} size="sm" />
            </div>
            <div className="text-lg font-bold text-gray-800">{count}</div>
            <div className="text-xs text-gray-600">{label}</div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-1">
        <span className="text-sm font-medium text-gray-600">总数:</span>
        <span className="text-lg font-bold text-blue-600">{total}</span>
      </div>
      {stats.map(({ status, count, label }) => (
        <div
          key={status}
          className={`flex items-center gap-2 ${
            onStatusClick ? 'cursor-pointer hover:opacity-80' : ''
          }`}
          onClick={() => onStatusClick?.(status)}
        >
          <EnhancedStatusIndicator status={status} size="sm" />
          <span className="text-sm font-medium text-gray-700">{count}</span>
        </div>
      ))}
    </div>
  );
};

export default EnhancedStatusIndicator;
