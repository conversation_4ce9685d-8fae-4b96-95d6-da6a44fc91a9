import { UploadService } from './services/UploadService';
import { TIMELINE_COMPONENT } from './timeline-component';

// 配置上传服务
const uploadService = new UploadService({
  cdnEndpoint: 'https://ife.bytedance.net/cdn/upload',
  timeout: 30000,
  enableCompression: true,
  compressionLevel: 6,
  enableLogging: true,
  maxRetries: 3,
  retryInterval: 1000,
  playgroundBaseUrl: 'https://playground.cn.goofy.app/',
  mockMode: false,
  cdnDomain: 'lf3-static.bytedance.com',
  directoryPath: 'so-web-code',
  contactEmail: '<EMAIL>'
});

// 演示上传和获取playground URL
export async function uploadTimelineComponent() {
  try {
    // 构建文件结构
    const fileStructure = {
      id: `timeline_${Date.now()}`,
      name: 'timeline-component',
      files: [
        {
          path: 'index.ttml',
          content: extractFileContent(TIMELINE_COMPONENT, 'index.ttml'),
          type: 'ttml' as const
        },
        {
          path: 'index.ttss',
          content: extractFileContent(TIMELINE_COMPONENT, 'index.ttss'),
          type: 'ttss' as const
        },
        {
          path: 'index.js',
          content: extractFileContent(TIMELINE_COMPONENT, 'index.js'),
          type: 'js' as const
        },
        {
          path: 'index.json',
          content: extractFileContent(TIMELINE_COMPONENT, 'index.json'),
          type: 'json' as const
        },
        {
          path: 'lynx.config.json',
          content: extractFileContent(TIMELINE_COMPONENT, 'lynx.config.json'),
          type: 'json' as const
        },
        {
          path: 'configTemplate.json',
          content: extractFileContent(TIMELINE_COMPONENT, 'configTemplate.json'),
          type: 'json' as const
        }
      ],
      metadata: {
        title: '发展历程时间线组件',
        description: '基于用户提供的图片设计的时间线组件',
        author: 'AI Assistant',
        version: '1.0.0',
        framework: 'lynx',
        createdAt: Date.now()
      }
    };

    // 上传到CDN
    const uploadResult = await uploadService.uploadToCDN(fileStructure);
    
    // 生成playground URL
    const playgroundUrl = uploadService.buildPlaygroundUrl(
      uploadResult.cdnUrl,
      'ttml-nodiff',
      'preview',
      '3.6.0-beta.2'
    );
    
    console.log('上传成功！');
    console.log('CDN URL:', uploadResult.cdnUrl);
    console.log('Playground URL:', playgroundUrl);
    
    return {
      success: true,
      cdnUrl: uploadResult.cdnUrl,
      playgroundUrl: playgroundUrl,
      uploadResult
    };
    
  } catch (error) {
    console.error('上传失败:', error);
    return {
      success: false,
      error: error.message,
      playgroundUrl: null
    };
  }
}

// 从组件字符串中提取指定文件内容
function extractFileContent(componentString: string, filePath: string): string {
  const lines = componentString.split('\n');
  let inTargetFile = false;
  let content = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检查是否是目标文件的开始
    if (line.includes(`FILE path="${filePath}"`)) {
      inTargetFile = true;
      continue;
    }
    
    // 检查是否是文件结束
    if (inTargetFile && line.trim() === 'FILE') {
      break;
    }
    
    // 收集文件内容
    if (inTargetFile) {
      content += line + '\n';
    }
  }
  
  return content.trim();
}

// 模拟上传结果 (如果实际上传失败时使用)
export function generateMockPlaygroundUrl(): string {
  const mockCdnUrl = `https://lf3-static.bytedance.com/so-web-code/timeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.zip`;
  return uploadService.buildPlaygroundUrl(mockCdnUrl);
}