import { QueryContext, BuilderModule } from './types';

export class ExecutionProtocolBuilder implements BuilderModule {
  build(context: QueryContext): string {
    const priorityMode =
      context.type === 'educational'
        ? 'PRIORITIZE cognitive comfort and learning effectiveness'
        : context.type === 'search_information'
          ? 'PRIORITIZE information discoverability and decision confidence'
          : 'BALANCE creative innovation with technical precision';

    return `
# 🎯 EXECUTION PROTOCOL (Final Instructions)

## IMPLEMENTATION SEQUENCE (MANDATORY)
1. **DEEP ANALYSIS & QUERY INTELLIGENCE**: 
   - MANDATORY: 在生成数据之前，必须进行深度query意图分析
   - 用户心理揣摩：深入理解手机搜索用户的真实需求和心理预期
   - 内容类型识别：判断用户期望的信息类型（教学类/笔记类/策略类/娱乐类/工具类等）
   - 隐晦表述解析：增强对复杂、含蓄、间接表述的深层理解能力
   - Key_Point提取：从用户query中识别并提取核心关键点和潜在需求
   - 意图理解验证：确保准确理解用户真实意图，避免偏离用户预期
   - 传递完整信息：将识别出的Key_Point与原始Query一同传给Planner
   - 手机场景优化：考虑移动端用户的使用场景、时间限制和注意力特点
   - Understand user's core functional and emotional needs beyond surface requirements
2. **ARCHITECTURE DESIGN**: Plan optimal information flow and component hierarchy for mobile-first experience
3. **TECHNICAL IMPLEMENTATION**: Use required Lynx components with 100% constraint compliance and zero tolerance for violations
4. **INTEGRATION COMPLETE**: ${context.isLightChartNeeded ? 'Include complete LightChart implementation with proper data validation' : ''}${context.isFontAwesomeNeeded ? 'Include complete Font Awesome integration with mandatory @font-face configuration' : ''}
5. **OPTIMIZATION VERIFY**: Ensure mobile performance excellence and seamless user experience
6. **CONSTRAINT VALIDATION**: Verify every technical requirement and syntax rule with comprehensive testing

## QUALITY EXCELLENCE STANDARDS
- **Visual Excellence**: Pixel-perfect design with consistent, professional styling and attention to micro-details
- **Interaction Excellence**: Smooth, responsive, intuitive touch-first user experience with delightful micro-interactions
- **Technical Excellence**: Clean, efficient, maintainable, production-ready code following best practices
- **Performance Excellence**: Fast loading, smooth 60fps animations, optimal memory usage, and efficient resource management
- **Accessibility Excellence**: Inclusive design supporting diverse user needs and assistive technologies

## MANDATORY VERIFICATION PROTOCOL
□ **File Structure**: Complete 5-file Lynx suite (index.ttml, index.ttss, index.js, index.json, lynx.config.json)
□ **Component Compliance**: NO HTML tags (div, span, img), ONLY Lynx components (view, text, image, scroll-view)
□ **Event Syntax**: Correct bindtap syntax (NOT bindtap), proper event handling patterns with error boundaries
□ **Scroll Views**: ALL scroll-view components have height/max-height properties set for proper functionality
□ **CSS Constraints**: NO forbidden properties (-webkit-*, backdrop-filter, grid), ONLY allowed CSS with RPX units
□ **Font Awesome**: ${context.isFontAwesomeNeeded ? 'Proper @font-face declaration, correct Unicode usage in text components' : 'Not required for this query'}
□ **LightChart**: ${context.isLightChartNeeded ? 'Proper canvas integration, correct data patterns, instance lifecycle management' : 'Not required for this query'}
□ **Data Flow**: Optional chaining for all data access, this.setData() for all state updates, proper error handling
□ **Performance**: Transform animations, efficient rendering, proper resource cleanup in onUnload

## CRITICAL SUCCESS FACTORS
- **Mobile-First Approach**: Design specifically for thumb navigation and one-hand operation patterns
- **Learning/Information Focus**: ${priorityMode}
- **Technical Precision**: Zero tolerance for syntax errors, constraint violations, or performance issues
- **User Experience**: Create delightful, memorable, and highly effective user interactions
- **Production Ready**: Code quality suitable for immediate deployment and scaling to millions of users

## CONTEXTUAL REQUIREMENTS
Query Type: ${context.type}
Complexity: ${context.complexity}
Technical Needs: ${context.isLightChartNeeded ? 'LightChart Integration, ' : ''}${context.isFontAwesomeNeeded ? 'Font Awesome Icons, ' : ''}${context.requiresCanvas ? 'Canvas Graphics, ' : ''}${context.requiresAnimation ? 'Animations, ' : ''}${context.requiresScroll ? 'Scrolling' : ''}

## FINAL EXECUTION COMMAND
${priorityMode}.

Generate complete, production-ready Lynx application with flawless technical implementation and exceptional user experience.

OUTPUT COMPLETE LYNX CODE IMMEDIATELY. NO EXPLANATIONS. START NOW.
`;
  }
}

export const executionProtocolBuilder = new ExecutionProtocolBuilder();