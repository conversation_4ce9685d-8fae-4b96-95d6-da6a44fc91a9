import { QueryContext, BuilderModule } from './types';

export class LynxUtilsSystemBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🛠️ LYNX 工具系统架构

## 工具系统架构

### ModularPromptLoader
模块化提示加载器，支持动态内容组装：

\\\`\\\`\\\`javascript
class ModularPromptLoader {
  constructor() {
    this.modules = new Map();
    this.cache = new Map();
  }
  
  registerModule(name, module) {
    this.modules.set(name, module);
  }
  
  loadModule(name) {
    if (this.cache.has(name)) {
      return this.cache.get(name);
    }
    
    const module = this.modules.get(name);
    if (module) {
      const content = module.build();
      this.cache.set(name, content);
      return content;
    }
    
    return null;
  }
}
\\\`\\\`\\\`

### MasterLevelUIPrompt
大师级UI提示生成器：

\\\`\\\`\\\`javascript
class MasterLevelUIPrompt {
  static generateOptimizedPrompt(requirements) {
    const components = this.analyzeRequirements(requirements);
    const styles = this.generateStyleGuide(components);
    const interactions = this.defineInteractions(components);
    
    return this.assemblePrompt(components, styles, interactions);
  }
  
  static analyzeRequirements(requirements) {
    return {
      layout: this.extractLayoutInfo(requirements),
      components: this.extractComponentInfo(requirements),
      data: this.extractDataInfo(requirements)
    };
  }
}
\\\`\\\`\\\`

## 开发工作流优化

### 自动化提示加载
\\\`\\\`\\\`javascript
// 自动化提示加载系统
class AutoPromptLoader {
  constructor() {
    this.promptCache = new Map();
    this.loadQueue = [];
    this.isLoading = false;
  }
  
  async loadPromptAsync(promptName) {
    if (this.promptCache.has(promptName)) {
      return this.promptCache.get(promptName);
    }
    
    this.loadQueue.push(promptName);
    
    if (!this.isLoading) {
      await this.processLoadQueue();
    }
    
    return this.promptCache.get(promptName);
  }
  
  async processLoadQueue() {
    this.isLoading = true;
    
    while (this.loadQueue.length > 0) {
      const promptName = this.loadQueue.shift();
      const prompt = await this.fetchPrompt(promptName);
      this.promptCache.set(promptName, prompt);
    }
    
    this.isLoading = false;
  }
}
\\\`\\\`\\\`

### 错误恢复机制
\\\`\\\`\\\`javascript
class ErrorRecoveryManager {
  constructor() {
    this.fallbackPrompts = new Map();
    this.retryConfig = {
      maxRetries: 3,
      backoffMultiplier: 2,
      baseDelay: 1000
    };
  }
  
  async executeWithRecovery(operation, fallbackKey) {
    let attempt = 0;
    
    while (attempt < this.retryConfig.maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempt++;
        
        if (attempt === this.retryConfig.maxRetries) {
          return this.getFallback(fallbackKey);
        }
        
        const delay = this.retryConfig.baseDelay * 
          Math.pow(this.retryConfig.backoffMultiplier, attempt - 1);
        await this.sleep(delay);
      }
    }
  }
  
  getFallback(key) {
    return this.fallbackPrompts.get(key) || this.getDefaultFallback();
  }
}
\\\`\\\`\\\`

## SystemInfo 全局变量详解

### 平台信息检测
\\\`\\\`\\\`javascript
// 平台信息获取
const platformInfo = {
  platform: SystemInfo.platform, // "ios" | "android" | "web"
  version: SystemInfo.version, // 系统版本
  model: SystemInfo.model, // 设备型号
  brand: SystemInfo.brand, // 设备品牌
  language: SystemInfo.language, // 系统语言
  system: SystemInfo.system // 操作系统
};

// 平台适配逻辑
function adaptToPlatform() {
  const { platform } = SystemInfo;
  
  switch (platform) {
    case 'ios':
      return {
        statusBarHeight: 44,
        navigationBarHeight: 44,
        tabBarHeight: 49
      };
    case 'android':
      return {
        statusBarHeight: 25,
        navigationBarHeight: 48,
        tabBarHeight: 48
      };
    default:
      return {
        statusBarHeight: 0,
        navigationBarHeight: 40,
        tabBarHeight: 40
      };
  }
}
\\\`\\\`\\\`

### 屏幕信息获取
\\\`\\\`\\\`javascript
// 屏幕显示信息
const screenInfo = {
  screenWidth: SystemInfo.screenWidth, // 屏幕宽度(px)
  screenHeight: SystemInfo.screenHeight, // 屏幕高度(px)
  windowWidth: SystemInfo.windowWidth, // 可使用窗口宽度
  windowHeight: SystemInfo.windowHeight, // 可使用窗口高度
  pixelRatio: SystemInfo.pixelRatio, // 设备像素比
  statusBarHeight: SystemInfo.statusBarHeight, // 状态栏高度
  safeArea: SystemInfo.safeArea // 安全区域
};

// 响应式设计计算
function calculateResponsiveUnits() {
  const { screenWidth, pixelRatio } = SystemInfo;
  
  return {
    rpxRatio: screenWidth / 750, // RPX转换比例
    vwUnit: screenWidth / 100, // vw单位
    remBase: screenWidth / 20, // rem基准
    isSmallScreen: screenWidth <= 375,
    isMediumScreen: screenWidth > 375 && screenWidth <= 414,
    isLargeScreen: screenWidth > 414
  };
}
\\\`\\\`\\\`

### 网络性能监测
\\\`\\\`\\\`javascript
// 网络性能信息
const networkInfo = {
  networkType: SystemInfo.networkType, // 网络类型
  isConnected: SystemInfo.isConnected, // 是否连接
  isMobileNetwork: SystemInfo.isMobileNetwork, // 是否移动网络
  effectiveType: SystemInfo.effectiveType // 有效网络类型
};

// 网络适配策略
function adaptToNetwork() {
  const { networkType } = SystemInfo;
  
  const strategies = {
    'wifi': {
      imageQuality: 'high',
      preloadDistance: 3,
      cacheSize: '50MB'
    },
    '4g': {
      imageQuality: 'medium',
      preloadDistance: 2,
      cacheSize: '30MB'
    },
    '3g': {
      imageQuality: 'low',
      preloadDistance: 1,
      cacheSize: '20MB'
    },
    '2g': {
      imageQuality: 'lowest',
      preloadDistance: 0,
      cacheSize: '10MB'
    }
  };
  
  return strategies[networkType] || strategies['3g'];
}
\\\`\\\`\\\`

## 第三方库集成

### 图表库集成
\\\`\\\`\\\`javascript
// LightChart 集成管理
class ChartIntegrationManager {
  constructor() {
    this.chartInstances = new Map();
    this.defaultConfig = {
      animation: true,
      responsive: true,
      maintainAspectRatio: false
    };
  }
  
  createChart(canvasId, type, data, options = {}) {
    const config = Object.assign({}, this.defaultConfig, options);
    
    const chart = new LynxChart({
      canvasName: canvasId,
      width: config.width || 300,
      height: config.height || 200
    });
    
    this.chartInstances.set(canvasId, chart);
    
    switch (type) {
      case 'line':
        return chart.line(data, config);
      case 'bar':
        return chart.bar(data, config);
      case 'pie':
        return chart.pie(data, config);
      default:
        throw new Error(\`Unsupported chart type: \${type}\`);
    }
  }
  
  destroyChart(canvasId) {
    const chart = this.chartInstances.get(canvasId);
    if (chart) {
      chart.destroy();
      this.chartInstances.delete(canvasId);
    }
  }
}
\\\`\\\`\\\`

### 开发工具集成
\\\`\\\`\\\`javascript
// 开发调试工具
class DebugTools {
  constructor() {
    this.isDebugMode = false;
    this.logs = [];
    this.performanceMarks = new Map();
  }
  
  enableDebug() {
    this.isDebugMode = true;
    this.injectDebugPanel();
  }
  
  log(message, type = 'info') {
    if (this.isDebugMode) {
      const logEntry = {
        timestamp: Date.now(),
        message,
        type,
        stack: new Error().stack
      };
      
      this.logs.push(logEntry);
      console.log(\`[DEBUG] \${message}\`);
    }
  }
  
  markPerformance(name) {
    this.performanceMarks.set(name, Date.now());
  }
  
  measurePerformance(startMark, endMark) {
    const start = this.performanceMarks.get(startMark);
    const end = this.performanceMarks.get(endMark);
    
    if (start && end) {
      const duration = end - start;
      this.log(\`Performance: \${startMark} -> \${endMark}: \${duration}ms\`);
      return duration;
    }
    
    return null;
  }
}
\\\`\\\`\\\`

### 性能监控
\\\`\\\`\\\`javascript
// 性能监控系统
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      renderTime: [],
      networkTime: [],
      memoryUsage: [],
      frameRate: []
    };
    this.isMonitoring = false;
  }
  
  startMonitoring() {
    this.isMonitoring = true;
    this.monitorRenderTime();
    this.monitorNetworkTime();
    this.monitorMemoryUsage();
    this.monitorFrameRate();
  }
  
  monitorRenderTime() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.entryType === 'measure') {
          this.metrics.renderTime.push({
            name: entry.name,
            duration: entry.duration,
            timestamp: entry.startTime
          });
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
  }
  
  getMetrics() {
    return {
      averageRenderTime: this.calculateAverage(this.metrics.renderTime),
      averageNetworkTime: this.calculateAverage(this.metrics.networkTime),
      memoryUsage: this.getLatestMemoryUsage(),
      frameRate: this.calculateFrameRate()
    };
  }
}
\\\`\\\`\\\`

## 最佳实践规范

### 工具使用规范
1. **模块化加载**: 使用 ModularPromptLoader 管理提示内容
2. **错误处理**: 始终使用 ErrorRecoveryManager 处理异步操作
3. **性能监控**: 在生产环境启用 PerformanceMonitor
4. **调试支持**: 开发环境使用 DebugTools

### 性能优化策略
1. **懒加载**: 按需加载工具模块
2. **缓存策略**: 使用智能缓存减少重复计算
3. **内存管理**: 及时清理不再使用的工具实例
4. **批处理**: 批量处理工具操作以提高效率

### 错误处理原则
1. **优雅降级**: 提供合理的默认值和后备方案
2. **错误边界**: 使用 try-catch 包装关键操作
3. **用户友好**: 将技术错误转换为用户可理解的信息
4. **日志记录**: 详细记录错误信息以便调试

这些工具系统确保了 Lynx 开发过程的高效性和可靠性，提供了完整的开发、调试和性能监控支持。
`;
  }
}

export const lynxUtilsSystemBuilder = new LynxUtilsSystemBuilder();