import { QueryContext, BuilderModule } from './types';

export class ModularPromptLoaderBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🔧 模块化提示加载器系统

## 模块化提示加载原理

### 核心架构
模块化提示加载器采用组件化设计，将大型提示拆分为可复用的模块单元：

\\\`\\\`\\\`javascript
class ModularPromptLoader {
  constructor() {
    this.modules = new Map();
    this.cache = new Map();
    this.dependencies = new Map();
    this.loadOrder = [];
  }
  
  // 注册模块
  registerModule(name, module, dependencies = []) {
    this.modules.set(name, module);
    this.dependencies.set(name, dependencies);
    this.updateLoadOrder();
  }
  
  // 加载单个模块
  async loadModule(name, context = {}) {
    const cacheKey = \`\${name}_\${JSON.stringify(context)}\`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const module = this.modules.get(name);
    if (!module) {
      throw new Error(\`Module \${name} not found\`);
    }
    
    // 加载依赖
    const dependencies = this.dependencies.get(name) || [];
    const loadedDependencies = await Promise.all(
      dependencies.map(dep => this.loadModule(dep, context))
    );
    
    // 构建模块内容
    const content = await module.build(context, loadedDependencies);
    this.cache.set(cacheKey, content);
    
    return content;
  }
  
  // 批量加载模块
  async loadModules(names, context = {}) {
    const results = await Promise.all(
      names.map(name => this.loadModule(name, context))
    );
    
    return results.join('\\n\\n');
  }
}
\\\`\\\`\\\`

### 动态内容组装
\\\`\\\`\\\`javascript
class DynamicContentAssembler {
  constructor(loader) {
    this.loader = loader;
    this.assemblyRules = new Map();
  }
  
  // 注册组装规则
  registerAssemblyRule(name, rule) {
    this.assemblyRules.set(name, rule);
  }
  
  // 根据查询动态组装内容
  async assembleContent(query, context) {
    const analysis = this.analyzeQuery(query);
    const requiredModules = this.determineRequiredModules(analysis);
    const assemblyRule = this.selectAssemblyRule(analysis);
    
    const moduleContents = await this.loader.loadModules(requiredModules, context);
    
    return assemblyRule.assemble(moduleContents, analysis);
  }
  
  // 分析查询需求
  analyzeQuery(query) {
    return {
      type: this.detectQueryType(query),
      complexity: this.assessComplexity(query),
      technicalRequirements: this.extractTechnicalRequirements(query),
      domain: this.identifyDomain(query)
    };
  }
  
  // 确定所需模块
  determineRequiredModules(analysis) {
    const baseModules = ['identity', 'creativity', 'constraints'];
    const conditionalModules = [];
    
    if (analysis.technicalRequirements.charts) {
      conditionalModules.push('lightchart');
    }
    
    if (analysis.technicalRequirements.icons) {
      conditionalModules.push('fontawesome');
    }
    
    if (analysis.complexity === 'high') {
      conditionalModules.push('advanced-patterns');
    }
    
    return [...baseModules, ...conditionalModules];
  }
}
\\\`\\\`\\\`

## 性能优化策略

### 懒加载机制
\\\`\\\`\\\`javascript
class LazyModuleLoader {
  constructor() {
    this.loadPromises = new Map();
    this.preloadQueue = [];
  }
  
  // 懒加载模块
  async lazyLoad(moduleName, context) {
    const key = \`\${moduleName}_\${JSON.stringify(context)}\`;
    
    if (this.loadPromises.has(key)) {
      return this.loadPromises.get(key);
    }
    
    const loadPromise = this.loadModuleAsync(moduleName, context);
    this.loadPromises.set(key, loadPromise);
    
    return loadPromise;
  }
  
  // 预加载常用模块
  preloadCommonModules() {
    const commonModules = [
      'identity', 'creativity', 'constraints', 'execution'
    ];
    
    commonModules.forEach(moduleName => {
      this.preloadQueue.push(() => this.lazyLoad(moduleName, {}));
    });
    
    this.processPreloadQueue();
  }
  
  // 处理预加载队列
  async processPreloadQueue() {
    const batchSize = 2;
    
    while (this.preloadQueue.length > 0) {
      const batch = this.preloadQueue.splice(0, batchSize);
      await Promise.all(batch.map(loader => loader()));
      
      // 避免阻塞主线程
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
}
\\\`\\\`\\\`

### 智能缓存策略
\\\`\\\`\\\`javascript
class IntelligentCache {
  constructor() {
    this.cache = new Map();
    this.accessTimes = new Map();
    this.hitCounts = new Map();
    this.maxSize = 100;
    this.ttl = 30 * 60 * 1000; // 30分钟
  }
  
  // 设置缓存
  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      lastAccess: Date.now()
    });
    
    this.accessTimes.set(key, Date.now());
    this.hitCounts.set(key, 0);
  }
  
  // 获取缓存
  get(key) {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.hitCounts.delete(key);
      return null;
    }
    
    // 更新访问信息
    entry.lastAccess = Date.now();
    this.accessTimes.set(key, Date.now());
    this.hitCounts.set(key, this.hitCounts.get(key) + 1);
    
    return entry.value;
  }
  
  // 淘汰最少使用的项
  evictLeastUsed() {
    let leastUsedKey = null;
    let leastUsedCount = Infinity;
    let oldestAccess = Infinity;
    
    for (const [key, hitCount] of this.hitCounts) {
      const accessTime = this.accessTimes.get(key);
      
      if (hitCount < leastUsedCount || 
          (hitCount === leastUsedCount && accessTime < oldestAccess)) {
        leastUsedKey = key;
        leastUsedCount = hitCount;
        oldestAccess = accessTime;
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.accessTimes.delete(leastUsedKey);
      this.hitCounts.delete(leastUsedKey);
    }
  }
}
\\\`\\\`\\\`

### 内存管理
\\\`\\\`\\\`javascript
class MemoryManager {
  constructor() {
    this.references = new WeakMap();
    this.cleanupTasks = [];
    this.memoryThreshold = 50 * 1024 * 1024; // 50MB
  }
  
  // 注册清理任务
  registerCleanupTask(task) {
    this.cleanupTasks.push(task);
  }
  
  // 监控内存使用
  monitorMemoryUsage() {
    if (performance.memory) {
      const usedMemory = performance.memory.usedJSHeapSize;
      
      if (usedMemory > this.memoryThreshold) {
        this.performCleanup();
      }
    }
  }
  
  // 执行清理
  async performCleanup() {
    for (const task of this.cleanupTasks) {
      try {
        await task();
      } catch (error) {
        console.error('Cleanup task failed:', error);
      }
    }
    
    // 建议垃圾回收
    if (window.gc) {
      window.gc();
    }
  }
  
  // 弱引用管理
  setWeakReference(object, data) {
    this.references.set(object, data);
  }
  
  getWeakReference(object) {
    return this.references.get(object);
  }
}
\\\`\\\`\\\`

## 模块版本管理

### 版本控制系统
\\\`\\\`\\\`javascript
class ModuleVersionManager {
  constructor() {
    this.versions = new Map();
    this.compatibility = new Map();
    this.migrations = new Map();
  }
  
  // 注册模块版本
  registerVersion(moduleName, version, content, dependencies = []) {
    const key = \`\${moduleName}@\${version}\`;
    
    this.versions.set(key, {
      name: moduleName,
      version,
      content,
      dependencies,
      timestamp: Date.now()
    });
    
    this.updateCompatibility(moduleName, version, dependencies);
  }
  
  // 获取模块版本
  getVersion(moduleName, version = 'latest') {
    if (version === 'latest') {
      return this.getLatestVersion(moduleName);
    }
    
    const key = \`\${moduleName}@\${version}\`;
    return this.versions.get(key);
  }
  
  // 获取最新版本
  getLatestVersion(moduleName) {
    const moduleVersions = Array.from(this.versions.entries())
      .filter(([key]) => key.startsWith(\`\${moduleName}@\`))
      .map(([, value]) => value)
      .sort((a, b) => this.compareVersions(b.version, a.version));
    
    return moduleVersions[0] || null;
  }
  
  // 版本比较
  compareVersions(a, b) {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart > bPart) return 1;
      if (aPart < bPart) return -1;
    }
    
    return 0;
  }
}
\\\`\\\`\\\`

### 热更新机制
\\\`\\\`\\\`javascript
class HotUpdateManager {
  constructor(loader) {
    this.loader = loader;
    this.watchers = new Map();
    this.updateQueue = [];
    this.isUpdating = false;
  }
  
  // 监控模块变化
  watchModule(moduleName, callback) {
    if (!this.watchers.has(moduleName)) {
      this.watchers.set(moduleName, []);
    }
    
    this.watchers.get(moduleName).push(callback);
  }
  
  // 触发模块更新
  async triggerUpdate(moduleName, newContent) {
    this.updateQueue.push({ moduleName, newContent });
    
    if (!this.isUpdating) {
      await this.processUpdateQueue();
    }
  }
  
  // 处理更新队列
  async processUpdateQueue() {
    this.isUpdating = true;
    
    while (this.updateQueue.length > 0) {
      const { moduleName, newContent } = this.updateQueue.shift();
      
      try {
        await this.updateModule(moduleName, newContent);
      } catch (error) {
        console.error(\`Failed to update module \${moduleName}:\`, error);
      }
    }
    
    this.isUpdating = false;
  }
  
  // 更新模块
  async updateModule(moduleName, newContent) {
    // 清除相关缓存
    this.loader.clearCache(moduleName);
    
    // 更新模块内容
    this.loader.updateModule(moduleName, newContent);
    
    // 通知监听者
    const callbacks = this.watchers.get(moduleName) || [];
    for (const callback of callbacks) {
      try {
        await callback(moduleName, newContent);
      } catch (error) {
        console.error(\`Callback failed for module \${moduleName}:\`, error);
      }
    }
  }
}
\\\`\\\`\\\`

## 调试和诊断工具

### 模块依赖分析
\\\`\\\`\\\`javascript
class DependencyAnalyzer {
  constructor(loader) {
    this.loader = loader;
  }
  
  // 分析模块依赖
  analyzeDependencies(moduleName) {
    const dependencies = this.loader.dependencies.get(moduleName) || [];
    const analysis = {
      direct: dependencies,
      indirect: [],
      circular: [],
      depth: 0
    };
    
    analysis.indirect = this.getIndirectDependencies(moduleName, new Set());
    analysis.circular = this.detectCircularDependencies(moduleName, new Set(), []);
    analysis.depth = this.calculateDependencyDepth(moduleName, new Set());
    
    return analysis;
  }
  
  // 获取间接依赖
  getIndirectDependencies(moduleName, visited) {
    const indirect = [];
    const direct = this.loader.dependencies.get(moduleName) || [];
    
    for (const dep of direct) {
      if (!visited.has(dep)) {
        visited.add(dep);
        indirect.push(dep);
        indirect.push(...this.getIndirectDependencies(dep, visited));
      }
    }
    
    return [...new Set(indirect)];
  }
  
  // 检测循环依赖
  detectCircularDependencies(moduleName, visited, path) {
    if (path.includes(moduleName)) {
      return [path.concat(moduleName)];
    }
    
    if (visited.has(moduleName)) {
      return [];
    }
    
    visited.add(moduleName);
    const dependencies = this.loader.dependencies.get(moduleName) || [];
    const circular = [];
    
    for (const dep of dependencies) {
      circular.push(...this.detectCircularDependencies(dep, visited, path.concat(moduleName)));
    }
    
    return circular;
  }
}
\\\`\\\`\\\`

这个模块化系统确保了提示内容的高效管理、动态加载和智能缓存，为 CognitiveOptimizedPrompt 提供了强大的基础架构支持。
`;
  }
}

export const modularPromptLoaderBuilder = new ModularPromptLoaderBuilder();