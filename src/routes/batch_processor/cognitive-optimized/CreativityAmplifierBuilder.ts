import { QueryContext, BuilderModule } from './types';

export class CreativityAmplifierBuilder implements BuilderModule {
  build(context: QueryContext, query: string = ''): string {
    return `
# 🎨 第一阶段：创意思维与设计规划 (Phase 1: Creative Thinking & Design Planning)

## 通用创意设计框架 (Universal Creative Design Framework)
所有查询都需要先进行创意思维，然后技术实现：

### 用户体验设计思维
- **同理心设计**: 深入理解用户场景、需求和痛点
- **视觉层次**: 通过排版、颜色、间距建立清晰的信息优先级
- **交互直觉**: 符合用户心理模型的自然交互方式
- **情感连接**: 通过细节和微交互创造积极的用户体验

### 移动端设计原则
- **拇指操作区域**: 重点关注屏幕下方75%的主要交互区域
- **单手操作**: 优先考虑单手使用模式和人体工程学
- **触摸目标**: 最小44px交互区域，确保精确点击
- **自然手势**: 直观的滑动、捏合、点击、长按交互模式

### 视觉设计心理学
- **认知负荷理论**: 将信息分块为7±2个段落，减少认知负担
- **双重编码理论**: 结合视觉和文字信息处理，增强记忆效果
- **渐进式披露**: 根据用户能力层次化信息复杂度
- **可访问性设计**: 支持多种能力和使用场景的包容性界面

## 创意构思过程 (Creative Ideation Process)
1. **场景分析**: 理解用户使用环境和上下文
2. **功能架构**: 设计核心功能和信息架构
3. **交互流程**: 规划用户操作路径和决策点
4. **视觉风格**: 确定色彩、字体、图标和布局风格
5. **细节优化**: 添加提升体验的微交互和过渡效果

## 查询分析与创意方向
查询内容："${query}"

基于以上创意框架，为此查询设计最具创新性和用户友好的移动端界面体验。
先进行创意思维和设计规划，然后进入Lynx技术实现阶段。
`;
  }
}

export const creativityAmplifierBuilder = new CreativityAmplifierBuilder();