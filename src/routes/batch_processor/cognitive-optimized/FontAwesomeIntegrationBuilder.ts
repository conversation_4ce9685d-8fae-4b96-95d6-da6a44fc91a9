import { QueryContext, BuilderModule } from './types';
import { FONT_AWESOME } from '../prompts/FontAwesome';

export class FontAwesomeIntegrationBuilder implements BuilderModule {
  build(context: QueryContext): string {
    if (!context.isFontAwesomeNeeded) {
      return '';
    }

    return `
# 🎨 FONT AWESOME INTEGRATION SPECIFICATION

## CRITICAL FONT AWESOME REQUIREMENTS
Font Awesome integration requires complete adherence to this specification:

${FONT_AWESOME}

## MANDATORY TTSS CONFIGURATION
Every project using Font Awesome MUST include this at the top of index.ttss:

\\\`\\\`\\\`css
@font-face {
  font-family: 'font-awesome-icon';
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
\\\`\\\`\\\`

## ICON USAGE PATTERNS
Always use with text component and proper Unicode:

\\\`\\\`\\\`html
<text class="fa-icon">\\uF015</text> <!-- Home icon -->
<text class="fa-icon">\\uF007</text> <!-- User icon -->
<text class="fa-icon">\\uF013</text> <!-- Settings icon -->
\\\`\\\`\\\`

## TTSS STYLING REQUIREMENTS
\\\`\\\`\\\`css
.fa-icon {
  font-family: 'font-awesome-icon';
  font-size: 32rpx;
  color: #333;
}
\\\`\\\`\\\`

## PERFORMANCE OPTIMIZATION
- Preload font in lynx.config.json
- Cache icon glyphs locally
- Use sprite sheets for multiple icons
- Implement proper font fallbacks
`;
  }
}

export const fontAwesomeIntegrationBuilder = new FontAwesomeIntegrationBuilder();