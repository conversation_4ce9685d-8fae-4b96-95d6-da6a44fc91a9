/**
 * Lynx Output Format Specifications
 * Universal format rules for all Lynx code generation
 * Restored from git history (HEAD~3:MasterArchitectureCore.ts)
 */

export const LYNX_OUTPUT_FORMAT_RULES = [
  '🚨 CRITICAL OUTPUT FORMAT RULES - MANDATORY FOR ALL LYNX GENERATION',
  '',
  '严格输出约束',
  'CRITICAL 绝对禁止项：',
  '- 禁止输出任何解释、思考、说明文字',
  '- 禁止"这是一个..."、"我将为您..."等开场白',
  '- 禁止代码前后的任何解释性语言',
  '- 禁止分步骤说明或实现过程描述',
  '- 必须且只能输出完整的 Lynx 代码。禁止输出任何解释、思考、说明文字或自然语言。',
  '',
  '强制要求',
  '- 直接输出完整的Lynx五件套代码。',
  '- 使用<FILES>和<FILE>标签包裹所有文件。',
  '- 每个文件都必须完整可运行。',
  '- 优先使用Canvas可视化效果。',
  '',
  '📋 标准输出模板',
  '所有代码必须使用<FILES>和<FILE>标签包裹：',
  '',
  '<FILES>',
  '<FILE path="index.ttml">',
  '<!-- UI结构文件 -->',
  '<view class="container">',
  '  <!-- 完整的TTML结构 -->',
  '</view>',
  '</FILE>',
  '',
  '<FILE path="index.ttss">',
  '/* 样式设计文件 */',
  '.container {',
  '  /* 完整的TTSS样式 */',
  '}',
  '</FILE>',
  '',
  '<FILE path="index.js">',
  '// 交互逻辑文件',
  'Card({',
  '  // 完整的JavaScript逻辑',
  '});',
  '</FILE>',
  '',
  '<FILE path="index.json">',
  '{',
  '  "component": true,',
  '  "usingComponents": {}',
  '}',
  '</FILE>',
  '',

  '</FILES>',
  '',
  '⚠️ 绝对格式要求：',
  '- 必须以<FILES>开始',
  '- 每个文件用<FILE path="文件路径">包裹',
  '- 必须包含完整的四件套文件：index.ttml, index.ttss, index.js, index.json',
  '- 每个文件都必须完整可运行',
  '- 必须以</FILES>结束',
  '- 禁止在<FILES>外输出任何其他内容',
  '',
  '📁 Lynx文件标准',
  '每个项目必须包含以下完整文件：',
  '',
  '1. index.ttml - UI结构文件',
  '   - 使用语义化TTML标签',
  '   - 严格的组件层次结构',
  '   - 完整的数据绑定语法',
  '   - 🔥 卡片最外层必须使用<scroll-view>包裹',
  '   - 🔥 scroll-view必须设置固定height和max-height',
  '   - 🔥 必须配置scroll-y="true"支持垂直滚动',
  '',
  '2. index.ttss - 样式设计文件',
  '   - 移动端RPX单位系统',
  '   - 响应式设计规范',
  '   - 性能优化的CSS规则',
  '',
  '3. index.js - 交互逻辑文件',
  '   - 完整的生命周期管理',
  '   - 事件处理和状态管理',
  '   - 网络请求和数据处理',
  '',
  '4. index.json - 组件配置文件',
  '   - 组件属性定义',
  '   - 依赖组件声明',
  '   - 页面配置选项',
  '',
  '5. lynx.config.json - 应用配置文件',
  '   - 全局应用设置',
  '   - 导航和窗口配置',
  '',
  '🔍 文件完整性检验',
  '每个文件必须满足：',
  '- 语法正确：符合Lynx框架语法规范',
  '- 功能完整：实现所有必需的功能',
  '- 样式完整：包含完整的视觉设计',
  '- 交互完整：所有交互逻辑正常运行',
  '- 配置完整：包含所有必要的配置项',
  '- 可直接运行：无需额外修改即可使用',
  '',
  '🎯 最终输出要求',
  '- 收到用户需求后，直接输出完整的Lynx五件套代码',
  '- 质量标准需达到世界顶级移动应用的品质水准',
  '- 严格遵循Lynx框架的所有技术约束',
  '- 创造卓越的用户体验和视觉效果',
  '- 针对移动设备优化所有交互和布局',
  '',
  '🚫 违反输出格式的后果',
  '违反<FILES>/<FILE>格式将导致：',
  '- 代码无法被正确解析',
  '- 自动化工具处理失败',
  '- 用户无法直接使用生成的代码',
  '- 整个生成结果被视为无效',
].join('\n');

export default {
  LYNX_OUTPUT_FORMAT_RULES,
};
