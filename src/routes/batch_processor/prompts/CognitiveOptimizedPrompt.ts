/**
 * 认知优化Prompt - 模块化重构版本重新导出
 * 为了保持向后兼容性，此文件重新导出模块化版本
 */

// 重新导出模块化版本的所有内容
export {
  CognitiveOptimizedPrompt,
  generateCognitiveOptimizedPrompt,
  analyzeCognitiveQuery,
  getCognitiveOptimizationStats,
  getOptimizedPromptStats
} from '../cognitive-optimized';

// 重新导出类型
export type { QueryContext, CognitiveStats } from '../cognitive-optimized';

// 重新导出默认实例
export { default } from '../cognitive-optimized';