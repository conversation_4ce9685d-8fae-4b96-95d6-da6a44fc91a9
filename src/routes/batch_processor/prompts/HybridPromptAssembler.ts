import { ModularPromptLoader, PromptModule } from './ModularPromptLoader';

/**
 * 定义核心提示词模块的标识符。
 * 这些模块包含框架的基础、核心约束和通用规则，是每次请求都必须加载的。
 */
const CORE_PROMPT_MODULES: PromptModule[] = [
  'VisualizationGuidance',    // 🎨 知识可视化专家角色 - 必须第一个加载
  'LynxFrameworkCore',
  'LynxStyleSystem',
  'LynxComponents',
];

/**
 * 定义符合RAG（检索增强生成）条件的模块标识符。
 * 这些模块内容更具体、更深入，将根据用户查询的关键词进行按需检索和加载。
 */
const RAG_ELIGIBLE_MODULES: Record<string, PromptModule[]> = {
  '数据同步,setData,线程': ['ThreadSynchronization'],
  '可视化,图表,设计': ['VisualizationGuidance'],
  '高级组件,Canvas,绘图': ['LynxComponents'],
  '最佳实践,示例,模式': ['BestPractices'],
  'LightChart,图表,工具': ['LynxUtilsSystem'],
  'TTML,严格约束': ['TTMLStrictConstraints'],
  'TTSS,CSS约束': ['TTSSStrictConstraints'],
};

/**
 * Hybrid RAG Prompt Assembler
 * 负责根据用户查询，动态组装核心提示词和通过RAG检索出的相关文档片段。
 */
export class HybridPromptAssembler {
  private static instance: HybridPromptAssembler;
  private promptLoader: ModularPromptLoader;

  private constructor() {
    this.promptLoader = ModularPromptLoader.getInstance();
  }

  public static getInstance(): HybridPromptAssembler {
    if (!HybridPromptAssembler.instance) {
      HybridPromptAssembler.instance = new HybridPromptAssembler();
    }
    return HybridPromptAssembler.instance;
  }

  /**
   * 加载并拼接所有核心提示词模块的内容。
   * @returns {string} 拼接后的核心提示词字符串。
   */
  private getCorePromptContent(): string {
    return CORE_PROMPT_MODULES
      .map(module => this.promptLoader.getModuleContent(module))
      .join('\n\n---\n\n');
  }

  /**
   * 根据用户查询分析需要加载的RAG模块。
   * @param {string} query - 用户查询字符串。
   * @returns {string} 拼接后的RAG模块内容字符串。
   */
  private getRagContent(query: string): string {
    const relevantModules: PromptModule[] = [];
    const queryLower = query.toLowerCase();
    for (const keywords in RAG_ELIGIBLE_MODULES) {
      if (keywords.split(',').some(keyword => queryLower.includes(keyword.toLowerCase()))) {
        relevantModules.push(...RAG_ELIGIBLE_MODULES[keywords]);
      }
    }

    // 去重，避免重复加载
    const uniqueModules = [...new Set(relevantModules)];

    if (uniqueModules.length === 0) {
      return '';
    }

    const ragContent = uniqueModules
      .map(module => this.promptLoader.getModuleContent(module))
      .join('\n\n---\n\n');
      
    return '\n 相关文档参考\n\n' + ragContent + '\n';
  }

  /**
   * 组装最终的混合提示词。
   * @param {string} userQuery - 用户的原始查询。
   * @returns {string} 包含核心提示词和按需加载的RAG内容的最终提示词。
   */
  public assemblePrompt(userQuery: string): string {
    const coreContent = this.getCorePromptContent();
    const ragContent = this.getRagContent(userQuery);

    // 在这里可以添加更复杂的组装逻辑，例如基于Token预算的动态截断等

    return `${coreContent}${ragContent}

🎯 输出执行指令
现在开始执行任务：

1. 分析用户需求
2. 根据决策树选择技术方案（View+TTSS 或 Canvas）
3. 直接输出完整的Lynx代码文件，使用<FILES>和<FILE>标签格式
4. 确保所有代码可直接运行，符合移动端最佳实践

⚠️ 严格输出约束 - 必须遵守
CRITICAL: 你必须且只能输出完整的 Lynx 代码。禁止输出任何解释、思考、说明文字或自然语言。

禁止输出任何非代码内容！立即开始编码！！`;
  }
}