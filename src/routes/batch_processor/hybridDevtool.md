HybridDevtool、Lynx和TemplateJS技术分析报告
1. HybridDevtool的架构和工作原理
1.1 HybridDevtool概述
HybridDevtool是一个用于跨端开发调试的工具平台，支持Lynx、Webview等多种环境的调试。它的核心功能是将移动端的原生界面和调试信息展示到PC端，实现远程调试能力，从而提高开发效率。
1.2 HybridDevtool架构设计
HybridDevtool采用了客户端-服务器架构，主要由以下部分组成：
1. 客户端调试面板：在移动端设备上提供全局调试面板和实例调试面板
2. PC平台：提供Web界面，用于远程调试和资源管理
3. WebSocket通信层：连接移动端和PC端，实现实时数据传输
4. 资源代理层：拦截和代理网络请求，替代Charles等工具
HybridDevtool架构图
graph TD
    MobileDevice["移动设备"] --> |"安装"| HybridDevtoolClient["HybridDevtool客户端"]
    HybridDevtoolClient --> |"WebSocket连接"| WebSocketServer["WebSocket服务器"]
    PCBrowser["PC浏览器"] --> |"访问"| HybridDevtoolWeb["HybridDevtool Web平台"]
    HybridDevtoolWeb --> |"WebSocket连接"| WebSocketServer
    
    subgraph "移动端"
        HybridDevtoolClient --> |"调试"| NativeApp["Native应用"]
        NativeApp --> |"包含"| LynxView["Lynx视图"]
        NativeApp --> |"包含"| WebView["WebView"]
    end
    
    subgraph "PC端"
        HybridDevtoolWeb --> |"展示"| UIPreview["UI预览"]
        HybridDevtoolWeb --> |"展示"| NetworkLogs["网络日志"]
        HybridDevtoolWeb --> |"展示"| JSBLogs["JSB日志"]
        HybridDevtoolWeb --> |"展示"| DevTools["开发者工具"]
        HybridDevtoolWeb --> |"展示"| ResourceManager["资源管理器"]
    end
    
    WebSocketServer --> |"数据传输"| HybridDevtoolWeb
    WebSocketServer --> |"指令传输"| HybridDevtoolClient
1.3 HybridDevtool如何将native界面显示在PC上
HybridDevtool将native界面显示在PC上的核心技术原理如下：
1.3.1 连接建立机制
1. 设备绑定流程：
  - 移动设备上的HybridDevtool客户端生成唯一标识符
  - PC端通过扫描二维码获取这个标识符
  - 两端使用这个标识符作为"房间号"连接到同一WebSocket服务器
2. WebSocket通信机制：
  - 使用基于UUID v4格式的"room"概念建立PC和移动设备之间的连接
  - 移动设备和PC网页都连接到同一个WebSocket服务器的同一个房间
  - 服务器负责转发两端之间的消息，确保实时通信
1.3.2 界面数据传输与重建
sequenceDiagram
    participant MobileApp as 移动应用
    participant DevtoolClient as HybridDevtool客户端
    participant WSServer as WebSocket服务器
    participant PCClient as PC浏览器
    
    MobileApp->>DevtoolClient: 渲染界面
    activate DevtoolClient
    DevtoolClient->>DevtoolClient: 捕获界面结构
    DevtoolClient->>DevtoolClient: 序列化DOM/视图结构
    DevtoolClient->>WSServer: 发送界面数据
    deactivate DevtoolClient
    
    WSServer->>PCClient: 转发界面数据
    activate PCClient
    PCClient->>PCClient: 解析界面数据
    PCClient->>PCClient: 重建DOM结构
    PCClient->>PCClient: 渲染界面预览
    deactivate PCClient
    
    MobileApp->>DevtoolClient: 界面更新
    activate DevtoolClient
    DevtoolClient->>DevtoolClient: 捕获变更
    DevtoolClient->>WSServer: 发送增量更新
    deactivate DevtoolClient
    
    WSServer->>PCClient: 转发增量更新
    activate PCClient
    PCClient->>PCClient: 应用增量更新
    PCClient->>PCClient: 更新界面预览
    deactivate PCClient
1. 界面结构捕获：
  - 对于Lynx视图：直接访问Lynx引擎内部的虚拟DOM结构
  - 对于WebView：使用类似Chrome DevTools Protocol的机制获取DOM结构
  - 捕获包括元素层级、属性、样式等完整信息
2. 数据序列化与传输：
  - 将捕获的界面结构转换为JSON格式
  - 对于大型结构，进行分块传输
  - 使用增量更新算法，只传输变化的部分，减少数据量
3. PC端界面重建：
  - 接收到界面数据后，在PC浏览器中重建DOM结构
  - 应用样式和布局信息
  - 使用iframe隔离不同的调试实例
1.3.3 交互同步机制
sequenceDiagram
    participant PCClient as PC浏览器
    participant WSServer as WebSocket服务器
    participant DevtoolClient as HybridDevtool客户端
    participant MobileApp as 移动应用
    
    PCClient->>PCClient: 用户点击元素
    activate PCClient
    PCClient->>WSServer: 发送元素选择事件
    deactivate PCClient
    
    WSServer->>DevtoolClient: 转发元素选择事件
    activate DevtoolClient
    DevtoolClient->>MobileApp: 高亮对应元素
    DevtoolClient->>DevtoolClient: 获取元素详细信息
    DevtoolClient->>WSServer: 返回元素详情
    deactivate DevtoolClient
    
    WSServer->>PCClient: 转发元素详情
    activate PCClient
    PCClient->>PCClient: 显示元素属性面板
    deactivate PCClient
    
    PCClient->>PCClient: 修改元素属性
    activate PCClient
    PCClient->>WSServer: 发送属性修改指令
    deactivate PCClient
    
    WSServer->>DevtoolClient: 转发属性修改指令
    activate DevtoolClient
    DevtoolClient->>MobileApp: 应用属性修改
    DevtoolClient->>WSServer: 返回修改结果
    deactivate DevtoolClient
    
    WSServer->>PCClient: 转发修改结果
    activate PCClient
    PCClient->>PCClient: 更新界面预览
    deactivate PCClient
1. 元素检查与选择：
  - PC端可以选择界面上的任意元素
  - 选择信息通过WebSocket发送到移动端
  - 移动端高亮对应元素，并返回详细属性信息
2. 属性修改与实时预览：
  - PC端修改元素属性或样式
  - 修改指令发送到移动端
  - 移动端应用修改并返回结果
  - PC端更新预览，实现所见即所得
3. 事件模拟与调试：
  - 支持在PC端模拟点击、滑动等事件
  - 事件指令发送到移动端执行
  - 移动端返回执行结果和状态变化
1.4 HybridDevtool功能概览
HybridDevtool提供了丰富的调试功能：
1. 宿主选择：支持选择不同的宿主环境（如抖音等）
2. 环境配置：展示和管理用户创建的环境配置
3. 资源代理：代理API和静态资源请求，替代Charles
4. 编辑配置：编辑代理配置，实现端上资源拦截
5. 网络日志：支持网络抓包，查看网络请求和响应
6. DevTool：支持Lynx调试工具的功能
7. 客户端日志：查看与端上相同的日志信息
8. Gecko信息：查看与端上相同的Gecko资源信息，可更新或删除指定资源
9. JSB调试：监控和分析JSB调用情况
2. Lynx代码调试的技术实现
2.1 Lynx概述
Lynx是一种跨平台的前端开发框架，用于构建高性能的移动应用界面。它采用类似Web的开发方式，但直接渲染为原生组件，实现了更好的性能和体验。
2.2 Lynx编译渲染流程
graph TD
    DevCode["开发者代码"] --> |"编译"| TemplateJS["Template.js"]
    TemplateJS --> |"下载"| MobileClient["移动客户端"]
    MobileClient --> |"Decode"| AppService["App Service"]
    MobileClient --> |"初始化数据"| LepusEngine["Lepus引擎"]
    AppService --> |"执行"| JSEngine["JS引擎"]
    JSEngine --> |"setState"| LepusEngine
    LepusEngine --> |"创建"| VirtualDOM["虚拟DOM"]
    VirtualDOM --> |"生成"| ElementTree["Element Tree"]
    ElementTree --> |"绘制"| NativeUI["Native UI"]
    
    JSEngine --> |"事件/回调"| StateUpdate["状态更新"]
    StateUpdate --> |"触发"| RadonDiff["Radon/Diff算法"]
    RadonDiff --> |"更新"| VirtualDOM
Lynx的编译渲染流程主要包括以下步骤：
1. 资源加载：
  - 客户端获取二进制文件template.js
  - 通过Decode模块将template.js反解析
  - 将解析出的app-service交给JS引擎执行
2. 初始化渲染：
  - 初始化客户端传输参数或默认数据
  - 交给Lepus引擎执行
  - 创建虚拟DOM、生成虚拟DOM树
  - 生成Element Tree
  - 最终绘制到Native组件中
3. 数据更新渲染：
  - JS引擎中发生事件（如点击、请求回调）后执行setState操作
  - 同步给Lepus引擎
  - 通过Radon/Diff算法更新虚拟DOM树
  - 重新绘制视图
2.3 Lynx与TemplateJS的关系
graph TD
    subgraph "开发阶段"
        DevCode["开发者代码<br/>(.vue/.js)"] --> |"编译"| TemplateJS["Template.js<br/>(二进制格式)"]
    end
    
    subgraph "运行阶段"
        TemplateJS --> |"加载"| ResourceLoader["ResourceLoader"]
        ResourceLoader --> |"缓存/内置"| CachedTemplate["缓存/内置模板"]
        ResourceLoader --> |"网络下载"| DownloadedTemplate["下载模板"]
        
        CachedTemplate --> |"解码"| Decoder["模板解码器"]
        DownloadedTemplate --> |"解码"| Decoder
        
        Decoder --> |"解析"| AppService["App Service<br/>(JS逻辑)"]
        Decoder --> |"解析"| UIDescription["UI描述"]
        
        AppService --> |"执行"| JSEngine["JS引擎"]
        UIDescription --> |"渲染"| LepusEngine["Lepus引擎"]
        
        JSEngine --> |"数据/事件"| LepusEngine
        LepusEngine --> |"映射"| NativeComponents["Native组件"]
    end
Lynx与TemplateJS的关系是核心与载体的关系：
2.3.1 TemplateJS作为载体
1. 定义与格式：
  - TemplateJS是Lynx应用的打包产物，是一种二进制格式的文件
  - 包含了应用的UI描述、逻辑代码和资源引用
  - 类似于Web中的HTML+CSS+JS的组合，但经过优化和二进制编码
2. 内容结构：
  - App Service部分：包含应用的业务逻辑，由JS引擎执行
  - UI描述部分：包含界面结构、样式和布局信息
  - 资源引用：如图片、字体等资源的引用信息
3. 加载与处理流程：
  - 客户端通过ResourceLoader加载template.js
  - 加载优先级：缓存 > 内置 > 网络下载
  - 加载完成后通过Decode模块解析
  - 解析出App Service和UI描述两部分
2.3.2 Native组件与TemplateJS的映射关系
1. 映射机制：
  - TemplateJS中的UI描述通过Lepus引擎解析
  - Lepus引擎创建虚拟DOM树
  - 虚拟DOM节点映射到对应的Native组件类型
  - 样式和属性应用到Native组件上
2. 组件映射表：
  - 文本元素 → Native文本组件
  - 容器元素 → Native容器组件
  - 图片元素 → Native图片组件
  - 列表元素 → Native列表组件
  - 输入元素 → Native输入组件
3. 事件与数据绑定：
  - JS逻辑中的事件监听器绑定到Native组件的事件
  - 数据变化通过setState触发虚拟DOM更新
  - 虚拟DOM更新后，通过Diff算法计算最小变更集
  - 应用变更到对应的Native组件
2.3.3 调试过程中的映射关系
sequenceDiagram
    participant Dev as 开发者
    participant HybridDevtool as HybridDevtool
    participant JSEngine as JS引擎
    participant LepusEngine as Lepus引擎
    participant NativeUI as Native UI
    
    Dev->>HybridDevtool: 选择元素
    activate HybridDevtool
    HybridDevtool->>LepusEngine: 查询元素信息
    LepusEngine->>HybridDevtool: 返回虚拟DOM节点
    HybridDevtool->>JSEngine: 查询对应源代码
    JSEngine->>HybridDevtool: 返回源代码位置
    HybridDevtool->>Dev: 显示元素与源码映射
    deactivate HybridDevtool
    
    Dev->>HybridDevtool: 修改属性
    activate HybridDevtool
    HybridDevtool->>JSEngine: 更新状态
    JSEngine->>LepusEngine: 触发重渲染
    LepusEngine->>NativeUI: 更新组件
    NativeUI->>HybridDevtool: 渲染完成
    HybridDevtool->>Dev: 显示更新结果
    deactivate HybridDevtool
1. 源码映射：
  - HybridDevtool可以将运行时的Native组件映射回源代码位置
  - 开发者可以看到元素对应的源代码行号和文件
2. 实时编辑：
  - 修改源代码或属性时，变更会实时应用到Native组件
  - 支持热重载，无需重启应用
3. 状态检查：
  - 可以查看组件的当前状态和属性
  - 支持查看数据流向和事件传递路径
2.4 Lynx调试技术实现
Lynx的调试技术实现主要依赖于以下机制：
1. 资源加载拦截：
  - ResourceLoader模块处理运行时资源获取
  - 对接Gecko的资源能力
  - 重写Falcon的离线拦截逻辑
  - 直接对接TTNetworkDownloader获取CDN资源
2. 预加载与优化：
  - 支持templateJS资源预加载
  - 支持templateBundle预解码(preDecode)
  - 支持templateRender预排版
3. JSB调试：
  - 监控JSB调用情况
  - 查看调用参数和返回值
  - 分析调用耗时
3. TemplateJS的作用机制及Web渲染可能性
3.1 TemplateJS概述
TemplateJS在Lynx框架中是一个关键概念，它是Lynx应用的打包产物，包含了应用的UI结构、样式和逻辑代码。需要注意的是，这里的TemplateJS与一般的JavaScript模板引擎（如art-template等）不同，它是Lynx特有的概念。
3.2 TemplateJS的作用机制
graph TD
    subgraph "TemplateJS内部结构"
        AppLogic["应用逻辑<br/>(JS代码)"]
        UIStructure["UI结构<br/>(类似HTML)"]
        StyleInfo["样式信息<br/>(类似CSS)"]
        ResourceRefs["资源引用<br/>(图片/字体等)"]
    end
    
    TemplateJS["Template.js文件"] --> |"包含"| AppLogic
    TemplateJS --> |"包含"| UIStructure
    TemplateJS --> |"包含"| StyleInfo
    TemplateJS --> |"包含"| ResourceRefs
    
    TemplateJS --> |"加载"| LynxEngine["Lynx引擎"]
    LynxEngine --> |"执行"| AppLogic
    LynxEngine --> |"渲染"| UIStructure
    LynxEngine --> |"应用"| StyleInfo
    LynxEngine --> |"加载"| ResourceRefs
    
    LynxEngine --> |"输出"| NativeUI["Native UI"]
TemplateJS的作用机制包括：
1. 资源封装：
  - 将应用的UI结构、样式和逻辑代码打包成一个二进制文件
  - 支持资源的离线化和缓存
  - 优化文件大小，减少网络传输开销
2. 跨平台渲染：
  - 通过Lynx引擎将TemplateJS解析为平台原生组件
  - 实现一次开发，多平台运行
  - 保持统一的开发体验和一致的渲染效果
3. 高效渲染：
  - 预编译和优化，减少运行时解析开销
  - 支持增量更新，提高渲染效率
  - 通过Radon/Diff算法优化重渲染性能
3.3 TemplateJS是否可以直接在Web上渲染
graph TD
    TemplateJS["Template.js"] --> |"可能路径1"| WebAdapter["Web适配层"]
    WebAdapter --> |"转换"| WebDOM["Web DOM"]
    
    TemplateJS --> |"可能路径2"| Compiler["编译转换"]
    Compiler --> |"生成"| WebComponents["Web组件"]
    
    TemplateJS --> |"可能路径3"| SSR["服务端渲染"]
    SSR --> |"生成"| HTML["HTML"]
    
    WebDOM --> |"渲染"| Browser["浏览器"]
    WebComponents --> |"渲染"| Browser
    HTML --> |"渲染"| Browser
根据收集的信息，关于TemplateJS是否可以直接在Web上渲染，可以得出以下结论：
3.3.1 理论上可行但需要适配
1. 中间格式特性：
  - TemplateJS本质上是一种中间格式，包含UI描述和逻辑代码
  - 这种格式理论上可以被转换为任何目标平台的代码，包括Web
2. 技术可行性分析：
  - 需要开发Web版的Lynx引擎或适配层
  - 将TemplateJS中的UI描述转换为Web DOM结构
  - 将样式映射到CSS
  - 将事件处理器绑定到DOM事件
3.3.2 工程化支持证据
1. Speedy工程化方案：
  - 文档中提到"服务端渲染支持"，暗示TemplateJS可能有Web渲染路径
  - "js代码分割(将js从主templatejs拆出，支持js单独缓存和离线化）"表明TemplateJS结构可拆分重组
2. Vue模板转换：
  - "实现Vue template转换JSX"的相关文档表明存在模板转换技术
  - 这种转换技术可能是将Lynx的TemplateJS转换为Web可用格式的基础
3. 编译优化：
  - 支持"编译器预渲染"功能
  - 支持"服务端编译需求"和"web编译需求"
  - 这些功能暗示了TemplateJS与Web环境的兼容性考虑
3.3.3 潜在实现路径
1. Web适配层方案：
  - 开发一个Web版的Lynx引擎
  - 在浏览器中解析TemplateJS
  - 将虚拟DOM映射到Web DOM
  - 优点：保持与Native版本的一致性
  - 缺点：需要在浏览器中加载额外的引擎代码
2. 编译转换方案：
  - 开发编译工具，将TemplateJS转换为标准Web代码
  - 生成HTML、CSS和JavaScript
  - 优点：无需额外运行时库，性能更好
  - 缺点：可能无法支持所有Lynx特性
3. 服务端渲染方案：
  - 在服务器上解析TemplateJS
  - 生成HTML并发送到客户端
  - 客户端进行水合(hydration)
  - 优点：首屏性能好，SEO友好
  - 缺点：需要服务器资源，交互体验可能受限
3.3.4 潜在挑战
1. API兼容性：
  - TemplateJS可能使用Lynx特有的API和组件
  - 需要在Web环境中提供对应实现或替代方案
2. 性能差异：
  - Native渲染性能通常优于Web渲染
  - Web版本可能无法达到相同的性能水平
3. 平台特定功能：
  - 某些依赖原生能力的功能可能难以在Web环境实现
  - 需要提供降级方案或替代实现
4. 调试体验：
  - Web环境的调试工具与Native环境不同
  - 需要适配调试工具链
4. 总结与关键技术点
4.1 HybridDevtool关键技术
1. WebSocket实时通信机制：
  - 基于"房间"概念的设备连接
  - 双向实时数据传输
  - 支持大规模数据分块传输
2. 界面数据序列化与重建：
  - 捕获Native界面结构
  - 序列化为传输格式
  - PC端重建界面预览
3. 远程调试协议设计：
  - 参考Chrome DevTools Protocol
  - 支持元素检查、网络监控、日志查看等功能
  - 自定义扩展支持Lynx特有功能
4. 资源代理与拦截技术：
  - 替代Charles等抓包工具
  - 支持资源替换和请求修改
  - 集成Gecko资源管理
4.2 Lynx与TemplateJS关键技术
1. 虚拟DOM与Native组件映射：
  - 将声明式UI描述转换为命令式Native渲染
  - 建立虚拟DOM节点与Native组件的对应关系
  - 支持双向数据绑定和事件处理
2. 二进制模板解析技术：
  - 高效解析TemplateJS二进制格式
  - 分离UI描述和逻辑代码
  - 优化内存占用和解析速度
3. Radon/Diff增量更新算法：
  - 计算虚拟DOM树的最小变更集
  - 只更新必要的Native组件
  - 提高重渲染性能
4. 资源加载链式优化：
  - 多级资源加载策略
  - 支持资源预加载和预解码
  - 优化首屏渲染性能
4.3 跨平台渲染技术展望
1. 统一的UI描述格式：
  - 探索更通用的UI描述格式
  - 支持多平台渲染，包括Native和Web
  - 减少开发和维护成本
2. 多平台渲染引擎适配：
  - 为不同平台开发专用渲染引擎
  - 保持API一致性
  - 优化各平台的性能表现
3. 性能与开发体验平衡：
  - 在保证性能的同时提供良好的开发体验
  - 支持热重载和实时预览
  - 提供完善的调试工具
4. 调试工具链完善：
  - 统一的调试体验
  - 跨平台的调试工具
  - 支持复杂场景的问题诊断
参考资料
1. Hybrid DevTool平台功能介绍
2. Hybrid DevTool之JSB相关功能
3. 简介小程序开发者工具与Hybrid Devtool的调试模型
4. 集成Chrome Devtools
5. Lynx新手开发指南
6. Hybrid架构春节基础能力
7. zh_guide_speedy_why
8. 实现Vue template转换JSX
9. 接入AnnieX的性能优化-Lynx场景



1. 术语介绍
远程调试模式
手机App和web平台通过websocket连接同一个远程服务器，使用指定的协议消息完成通信。（web平台和手机App都是通过WIFI与服务器通信的）
本地调试模式
手机App和web平台通过websocket连接同一个跑在PC本地的服务器，使用指定的协议消息完成通信。（由于服务器跑在本地，提升了web平台和服务器的通信速度）
USB调试模式

web平台通过websocket连接到跑在PC本地的服务器，手机App使用adb/usbmux通过USB连接到同一个本地服务器，使用指定的协议消息完成通信。（由于手机通过USB连接服务器，提升了手机App和服务器的通信速度）
Room
仅存在于远程调试模式中：服务器会创建一个房间，允许多台手机或多个PC应用加入房间相互通信
Client ID
服务器为每个客户端分配的唯一标识
Session ID
手机端为每个可调试实体（如LynxView、WebView）分配的唯一会话标识
CDP
全称 Chrome Devtools Protocol，是Chrome制定的一套标准的前端调试协议
插件
Web调试平台支持插件化扩展，可通过自定义类型消息传递数据，传递给Web端扩展面板消费
2. 交互流程
远程调试模式交互流程
[图片]
USB调试模式交互流程
[图片]
3. 通信协议
远程调试使用JSON格式的消息，包含2个字段，示例：
{"event": "xxx", "data": { ... } }
其中event字段必填，data字段可选。
除特殊说明外，客户端指web端和App端。
3.1 Initialize消息
服务端-->客户端，data字段包含一个整数作为客户端唯一标识，等于Customized类型消息的client_id。
示例：
{"event":"Initialize", "data":152}
3.2 Register消息
客户端-->服务端，data字段包含客户端上报的相关信息。
示例：
{
  "event":"Register",
  "data": {
    "id": 152,
    "info": {
        "App": "LynxExample",
        "AppVersion": "1.0.0",
        "deviceModel": "iPhone 11",
        "network": "WiFi",
        "osVersion": "14.2",
        "sdkVersion": "1.4.0"
    },
    "type": "runtime",   // runtime表示App, driver表示web
    "reconnect": true
  }
}
3.3 Registered
服务端-->客户端，无data字段。
示例：
{"event":"Registered"}
3.4 CreateRoom
Web客户端-->服务端，data字段是需要创建的房间号，若房间不存在会自动创建并加入房间，若房间已存在则会加入房间。
示例：
{"event": "CreateRoom", "data": "07525f1b-0813-4b29-9300-bb058a095e6c"}
3.5 RoomCreated
服务端-->Web客户端，data字段包含客户端信息及房间号。
{"event": "RoomCreated", "data": "07525f1b-0813-4b29-9300-bb058a095e6c"}
3.6 JoinRoom
客户端-->服务端，data字段是需要创建的房间号。若房间号不存在则会加入房间失败（大部分场景可用 CreateRoom 替代）。
示例：
{"event": "JoinRoom", "data": "07525f1b-0813-4b29-9300-bb058a095e6c"}
3.7 RoomJoined
服务端-->客户端，data字段包含客户端信息及房间号。
示例：
{
    "event":"RoomJoined",
    "data": {
        "id":158,
        "info": {
            "App":"LynxExample",
            "AppVersion":"1.0.0",
            "deviceModel":"iPhone 11",
            "network":"WiFi",
            "osVersion":"14.2",
            "sdkVersion":"1.4.0"
        },
        "type":"runtime",
        "room":"07525f1b-0813-4b29-9300-bb058a095e6c"
    }
}
3.8 ListClients
Web客户端-->服务端，data字段为undefined。
3.9 ClientList
服务端-->Web客户端，data字段包含房间内所有client信息。
[图片]
注：Driver代表平台，runtime代表客户端
3.10 RoomLeaved
服务端-->Web客户端，data字段包含客户端信息及房间号。
示例：
{
    "event":"RoomLeaved",
    "data": {
        "id":158,
        "info": {
            "App":"LynxExample",
            "AppVersion":"1.0.0",
            "deviceModel":"iPhone 11",
            "network":"WiFi",
            "osVersion":"14.2",
            "sdkVersion":"1.4.0"
        },
        "type":"runtime",
        "room":"07525f1b-0813-4b29-9300-bb058a095e6c"
    }
}
3.11 Customized
服务端<-->客户端，data字段包含3部分：
- type：消息类型
- sender：客户端ID
- data：各种类型的定制信息

3.11.1 SessionList类型
data中包含当前的页面列表，每一项都包含session_id、url和type（lynx/web/worker）。
示例：
{
    "event": "Customized",
    "data": {
        "type": "SessionList",
        "data": [
            {"type": "" | "web" | "worker", "session_id": 2, "url": "/var/mobile/Containers/Data/Application/EAF1F887-D1A0-414D-82C9-A1D2EE081919/Library/Caches/IESWebCache/6fe52b3d47cb62db6716952f51ec973e/playground/listPage/template.js"}
        ],
        "sender": 158
    }
}
3.11.2  CDP类型
data中包含原始的CDP消息。
******** web端主动发送给App端
示例：
// web端发送消息格式，message中带id属性
{
    "event":"Customized",
    "data":{
        "type":"CDP",
        "data": {
            "client_id":158,
            "session_id":3,
            "message": "{
                \"id\":1919,
                \"method\":\"Page.getResourceTree\", 
                \"params\": {...}
            }"
        },
        "sender":158
    }
}

// App端消息正常返回格式。id对应web端发送消息id
{
    "event":"Customized",
    "data":{
        "type":"CDP",
        "data": {
            "client_id":158,
            "session_id":3,
            "message": "{
                \"id:1919,
                \"result\":{...}
            }"
        },
        "sender":158
    }
}

// App端消息异常返回格式。id对应web端发送消息id
{
    "event":"Customized",
    "data":{
        "type":"CDP",
        "data": {
            "client_id":158,
            "session_id":3,
            "message": "{
                \"id\":1919,
                \"error\":{
                    \"code\": -32601, 
                    \"message\": \"...\"
                }
            }"
        },
        "sender":158
    }
}
3.11.2.2 App端主动发送给Web端
示例：
// App端主动发送给Web端消息，message不带id属性
{
    "event":"Customized",
    "data":{
        "type":"CDP",
        "data": {
            "client_id":158,
            "session_id":3,
            "message": "{
                \"method\":\"Page.getResourceTree\", 
                \"params\": {...}
            }"
        },
        "sender":158
    }
}



3.11.3  OpenCard类型
data中包含需要打开的页面的schema。
示例：
{
    "event":"Customized",
    "data": {
        "type":"OpenCard",
        "data": {
            "type":"url",
            "url":"aweme://lynxview/?surl=http%3A%2F%2F169.254.21.31%3A4001%2Fpages%2Fbuynow%2Fapp%2Ftemplate.js"
        },
        "sender":362
    },
    "from":362
}        
3.11.4  扩展插件类型
页面相关的消息需要带session_id，全局消息没有session_id字段
{
    "event":"Customized",
    "data":{
        "type":"*",
        "data": {
            "client_id": 147,
            "session_id": 3,
            "message": "xxxxx"
        },
        "sender":147
    },
    "from":147
}    
3.11.5  JS首行断点类型
{
    "event":"Customized",
    "data":{
        "type":"D2RStopAtEntry",
        "data": {
            "client_id": 147,
            "stop_at_entry": true
        },
        "sender":147
    }
}  
3.11.6 Lepus首行断点类型
{
    "event":"Customized",
    "data":{
        "type":"D2RStopLepusAtEntry",
        "data": {
            "client_id": 147,
            "stop_at_entry": true
        },
        "sender":147
    }
}  
3.11.7 全局开关配置类型
设置开关
{
    "event":"Customized",
    "data":{
        "type":"SetGlobalSwitch",
        "data": {
            "client_id": 147,
            "session_id": -1,
            "message": {
               "global_key" : "***",
               "global_value": true/false
            }
        },
        "sender":147
    },
    "from":147
}    
获取开关
{
    "event":"Customized",
    "data":{
        "type":"GetGlobalSwitch",
        "data": {
            "client_id": 147,
            "session_id": -1,
            "message": {
               "global_key" : "***",
            }
        },
        "sender":147
    },
    "from":147
}    
h. ListSession:
获取Session 列表，DebugRouter将返回SessionList(参考a)
{
    "event" : "Customized",
    "data" : {
       "data" : {
          "client_id" : -1
       },
       "sender" : 4,
       "type" : "ListSession"
    },
    "from" : 3
}
I. DebugRouter
DebugRouter 相关消息
"{'event': 'Customized','data': {'type': 'DebugRouter','data': {'client_id': -1,'session_id': -1,'message': {'id': -1,'method': 'DebugRouter.State','params': {'ConnectState': 1}}},'sender': -1}}"
Devtool CDP Extensions
UI  Inspector CDP: UITree Domain 

