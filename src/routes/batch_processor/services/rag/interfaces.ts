/**
 * RAG System Type Definitions and Interfaces
 * RAG系统类型定义和接口
 * 
 * Core interfaces for the AI-driven RAG system that intelligently
 * retrieves Lynx framework documentation based on AI analysis.
 * 基于AI分析智能检索Lynx框架文档的RAG系统核心接口
 */

// ===================================================================
// Core RAG Analysis Interfaces
// ===================================================================

/**
 * AI analysis result for determining required Lynx modules
 * AI分析结果，用于确定所需的Lynx模块
 */
export interface AIAnalysisResult {
  /** Whether API calls and lifecycle management are needed */
  needsAPI: boolean;
  
  /** Whether Canvas drawing functionality is needed */
  needsCanvas: boolean;
  
  /** Whether chart/visualization components are needed */
  needsCharts: boolean;
  
  /** Whether advanced UI components are needed */
  needsAdvanced: boolean;
  
  /** Whether best practices and optimization guides are needed */
  needsPractices: boolean;
  
  /** Whether visibility detection and monitoring APIs are needed */
  needsVisibility: boolean;
  
  /** Specific technical keywords extracted from the query */
  specificNeeds: string[];
  
  /** Confidence score of the analysis (0-1) */
  confidenceScore: number;
  
  /** Query complexity assessment */
  complexity: 'simple' | 'medium' | 'complex';
  
  /** Primary intent category */
  intent: 'ui_creation' | 'data_management' | 'visualization' | 'interaction' | 'optimization';
}

/**
 * RAG retrieval plan based on AI analysis
 * 基于AI分析的RAG检索计划
 */
export interface RAGRetrievalPlan {
  /** Whether RAG content retrieval is needed */
  needsRAG: boolean;
  
  /** List of module names to retrieve */
  moduleNames: string[];
  
  /** Maximum number of chunks to retrieve */
  maxChunks: number;
  
  /** Priority level for retrieval */
  priority: 'high' | 'medium' | 'low';
  
  /** Estimated token budget for RAG content */
  estimatedTokens: number;
  
  /** Reason for RAG decision */
  rationale: string;
}

// ===================================================================
// Lynx Module Definitions
// ===================================================================

/**
 * Lynx module metadata and configuration
 * Lynx模块元数据和配置
 */
export interface LynxModule {
  /** Unique module identifier */
  id: string;
  
  /** Human-readable module name */
  name: string;
  
  /** Module content (prompt text) */
  content: string;
  
  /** Module category */
  category: 'core' | 'api' | 'canvas' | 'charts' | 'practices' | 'visibility' | 'advanced';
  
  /** Estimated size in KB */
  sizeKB: number;
  
  /** Estimated token count */
  estimatedTokens: number;
  
  /** Keywords that trigger this module */
  triggerKeywords: string[];
  
  /** Usage scenarios description */
  usageScenarios: string[];
  
  /** Whether this is a core module (always loaded) */
  isCore: boolean;
  
  /** Module priority for retrieval */
  priority: number;
  
  /** Last updated timestamp */
  lastUpdated: Date;
}

/**
 * Core module configuration (always loaded)
 * 核心模块配置（始终加载）
 */
export interface CoreModuleSet {
  /** Framework core rules and output constraints */
  lynxFrameworkCore: LynxModule;
  
  /** Technical constraints and safety rules */
  technicalConstraints: LynxModule;
  
  /** Basic TTML components and tags */
  basicComponents: LynxModule;
  
  /** TTSS style system and CSS properties */
  ttssStyleSystem: LynxModule;
  
  /** Event system and mapping rules */
  eventSystem: LynxModule;
}

/**
 * RAG module configuration (context-dependent)
 * RAG模块配置（依赖上下文）
 */
export interface RAGModuleSet {
  /** API system and lifecycle management */
  lynxAPISystem?: LynxModule;
  
  /** Canvas drawing system */
  canvasSystem?: LynxModule;
  
  /** Chart library and visualization */
  lightChartSystem?: LynxModule;
  
  /** Best practices and optimization */
  bestPractices?: LynxModule;
  
  /** Visibility detection and monitoring */
  visibilityDetection?: LynxModule;
  
  /** Advanced components and patterns */
  advancedComponents?: LynxModule;
}

// ===================================================================
// Prompt Assembly Interfaces
// ===================================================================

/**
 * Assembled prompt with core and RAG content
 * 包含核心和RAG内容的组装提示词
 */
export interface AssembledPrompt {
  /** Core prompt content (always included) */
  corePrompt: string;
  
  /** RAG content (contextually included) */
  ragContent: string;
  
  /** Complete assembled prompt */
  fullPrompt: string;
  
  /** Metadata about the assembly process */
  metadata: PromptMetadata;
}

/**
 * Prompt assembly metadata
 * 提示词组装元数据
 */
export interface PromptMetadata {
  /** Total estimated token count */
  estimatedTokens: number;
  
  /** Core prompt token count */
  coreTokens: number;
  
  /** RAG content token count */
  ragTokens: number;
  
  /** Whether RAG content was included */
  hasRAGContent: boolean;
  
  /** List of included RAG modules */
  includedModules: string[];
  
  /** AI analysis that drove the assembly */
  analysisResult: AIAnalysisResult;
  
  /** Assembly timestamp */
  assembledAt: Date;
  
  /** Token optimization applied */
  optimizationApplied: boolean;
  
  /** Assembly duration in milliseconds */
  assemblyTimeMs: number;
}

// ===================================================================
// Performance and Caching Interfaces
// ===================================================================

/**
 * RAG system performance metrics
 * RAG系统性能指标
 */
export interface RAGPerformanceMetrics {
  /** Average AI analysis time in ms */
  avgAnalysisTimeMs: number;
  
  /** Average module retrieval time in ms */
  avgRetrievalTimeMs: number;
  
  /** Average prompt assembly time in ms */
  avgAssemblyTimeMs: number;
  
  /** Cache hit rate percentage */
  cacheHitRate: number;
  
  /** AI analysis accuracy percentage */
  analysisAccuracy: number;
  
  /** Token usage reduction percentage */
  tokenReduction: number;
  
  /** Number of queries processed */
  queriesProcessed: number;
  
  /** Success rate percentage */
  successRate: number;
}

/**
 * Cache entry for RAG results
 * RAG结果的缓存条目
 */
export interface RAGCacheEntry {
  /** Cache key (query hash) */
  key: string;
  
  /** Cached AI analysis result */
  analysisResult: AIAnalysisResult;
  
  /** Cached RAG retrieval plan */
  retrievalPlan: RAGRetrievalPlan;
  
  /** Cached assembled prompt */
  assembledPrompt: AssembledPrompt;
  
  /** Cache timestamp */
  timestamp: Date;
  
  /** Cache hit count */
  hitCount: number;
  
  /** Cache entry expiration time */
  expiresAt: Date;
}

// ===================================================================
// Error Handling and Fallback Interfaces
// ===================================================================

/**
 * RAG system error types
 * RAG系统错误类型
 */
export enum RAGErrorType {
  AI_ANALYSIS_FAILED = 'ai_analysis_failed',
  MODULE_RETRIEVAL_FAILED = 'module_retrieval_failed',
  PROMPT_ASSEMBLY_FAILED = 'prompt_assembly_failed',
  TOKEN_BUDGET_EXCEEDED = 'token_budget_exceeded',
  CACHE_ERROR = 'cache_error',
  NETWORK_ERROR = 'network_error',
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * RAG system error information
 * RAG系统错误信息
 */
export interface RAGError {
  /** Error type */
  type: RAGErrorType;
  
  /** Error message */
  message: string;
  
  /** Original error if available */
  originalError?: Error;
  
  /** Context information */
  context: {
    query?: string;
    step?: string;
    timestamp: Date;
    sessionId?: string;
  };
  
  /** Whether fallback was applied */
  fallbackApplied: boolean;
  
  /** Fallback strategy used */
  fallbackStrategy?: string;
}

/**
 * Fallback strategy configuration
 * 降级策略配置
 */
export interface FallbackConfig {
  /** Whether to enable AI analysis fallback */
  enableAnalysisFallback: boolean;
  
  /** Whether to enable module retrieval fallback */
  enableRetrievalFallback: boolean;
  
  /** Whether to use full prompt as ultimate fallback */
  enableFullPromptFallback: boolean;
  
  /** Maximum retry attempts */
  maxRetries: number;
  
  /** Fallback timeout in milliseconds */
  fallbackTimeoutMs: number;
}

// ===================================================================
// Configuration Interfaces
// ===================================================================

/**
 * RAG system configuration
 * RAG系统配置
 */
export interface RAGConfig {
  /** Whether RAG system is enabled */
  enabled: boolean;
  
  /** AI analysis configuration */
  aiAnalysis: {
    /** Claude4 endpoint for analysis */
    endpoint: string;
    
    /** Analysis timeout in milliseconds */
    timeoutMs: number;
    
    /** Analysis prompt template */
    promptTemplate: string;
    
    /** Minimum confidence threshold */
    minConfidence: number;
  };
  
  /** Token budget management */
  tokenBudget: {
    /** Maximum total tokens */
    maxTokens: number;
    
    /** Reserved tokens for response */
    reservedTokens: number;
    
    /** Core prompt token limit */
    coreTokenLimit: number;
    
    /** RAG content token limit */
    ragTokenLimit: number;
  };
  
  /** Cache configuration */
  cache: {
    /** Whether caching is enabled */
    enabled: boolean;
    
    /** Cache TTL in milliseconds */
    ttlMs: number;
    
    /** Maximum cache entries */
    maxEntries: number;
    
    /** Cache eviction strategy */
    evictionStrategy: 'lru' | 'lfu' | 'ttl';
  };
  
  /** Performance monitoring */
  monitoring: {
    /** Whether monitoring is enabled */
    enabled: boolean;
    
    /** Metrics collection interval in milliseconds */
    intervalMs: number;
    
    /** Whether to log detailed performance data */
    detailedLogging: boolean;
  };
  
  /** Fallback configuration */
  fallback: FallbackConfig;
}

// ===================================================================
// Service Interfaces
// ===================================================================

/**
 * Main RAG system interface
 * 主要RAG系统接口
 */
export interface RAGSystemInterface {
  /** Initialize the RAG system */
  initialize(config: RAGConfig): Promise<void>;
  
  /** Process a query and return assembled prompt */
  processQuery(query: string, sessionId?: string): Promise<AssembledPrompt>;
  
  /** Get current performance metrics */
  getMetrics(): RAGPerformanceMetrics;
  
  /** Clear caches */
  clearCache(): Promise<void>;
  
  /** Update configuration */
  updateConfig(config: Partial<RAGConfig>): void;
  
  /** Dispose and cleanup resources */
  dispose(): Promise<void>;
}

/**
 * AI analysis service interface
 * AI分析服务接口
 */
export interface AIAnalyzerInterface {
  /** Analyze query to determine RAG needs */
  analyzeQuery(query: string): Promise<AIAnalysisResult>;
  
  /** Get analysis prompt template */
  getAnalysisPrompt(query: string): string;
  
  /** Update analysis prompt template */
  updatePromptTemplate(template: string): void;
}

/**
 * Module retrieval service interface
 * 模块检索服务接口
 */
export interface ModuleRetrieverInterface {
  /** Retrieve modules based on analysis */
  retrieveModules(analysisResult: AIAnalysisResult): Promise<LynxModule[]>;
  
  /** Get core modules (always loaded) */
  getCoreModules(): CoreModuleSet;
  
  /** Get available RAG modules */
  getRAGModules(): RAGModuleSet;
  
  /** Update module content */
  updateModule(moduleId: string, content: string): void;
}

// ===================================================================
// Session Management Interfaces (New)
// ===================================================================

/**
 * Session context for multi-turn conversations
 * 多轮对话的会话上下文
 */
export interface SessionContext {
  /** Unique session identifier */
  sessionId: string;
  
  /** Session creation timestamp */
  createdAt: Date;
  
  /** Last activity timestamp */
  lastActivityAt: Date;
  
  /** Conversation history */
  conversationHistory: ConversationTurn[];
  
  /** Accumulated context from previous turns */
  accumulatedContext: string[];
  
  /** Session-specific preferences */
  preferences: SessionPreferences;
  
  /** Session metadata */
  metadata: Record<string, any>;
}

/**
 * Single conversation turn
 * 单轮对话
 */
export interface ConversationTurn {
  /** Turn sequence number */
  turnId: number;
  
  /** User query */
  query: string;
  
  /** AI analysis result for this turn */
  analysisResult: AIAnalysisResult;
  
  /** Selected modules for this turn */
  selectedModules: string[];
  
  /** Generated prompt for this turn */
  generatedPrompt: string;
  
  /** Turn timestamp */
  timestamp: Date;
  
  /** Performance metrics for this turn */
  performanceMetrics: TurnPerformanceMetrics;
}

/**
 * Session-specific preferences
 * 会话特定偏好设置
 */
export interface SessionPreferences {
  /** Preferred prompt style */
  promptStyle: 'concise' | 'detailed' | 'comprehensive';
  
  /** Maximum context length */
  maxContextLength: number;
  
  /** Whether to include previous context */
  includePreviousContext: boolean;
  
  /** Module selection strategy */
  moduleSelectionStrategy: 'conservative' | 'balanced' | 'aggressive';
  
  /** Custom module priorities */
  customModulePriorities: Record<string, number>;
}

/**
 * Performance metrics for a single turn
 * 单轮对话的性能指标
 */
export interface TurnPerformanceMetrics {
  /** Analysis time in milliseconds */
  analysisTimeMs: number;
  
  /** Context retrieval time in milliseconds */
  contextRetrievalTimeMs: number;
  
  /** Prompt assembly time in milliseconds */
  promptAssemblyTimeMs: number;
  
  /** Total tokens used */
  totalTokensUsed: number;
  
  /** Context tokens from previous turns */
  contextTokensUsed: number;
  
  /** New content tokens */
  newContentTokens: number;
}