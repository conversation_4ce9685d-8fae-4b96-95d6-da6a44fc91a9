/**
 * AI Analysis System for RAG Module Detection
 * AI分析系统用于RAG模块检测
 * 
 * Analyzes user queries using Claude4 to determine which Lynx modules
 * are needed for code generation. This is the core intelligence that
 * drives the RAG system's efficiency.
 * 使用Claude4分析用户查询，确定代码生成所需的Lynx模块
 */

import { AIAnalysisResult, AIAnalyzerInterface, RAGConfig } from './interfaces';

/**
 * AI-powered query analyzer for RAG module selection
 * 基于AI的查询分析器，用于RAG模块选择
 */
export class AIAnalyzer implements AIAnalyzerInterface {
  private config: RAGConfig;
  private analysisPromptTemplate: string;
  private apiEndpoint: string;
  private requestTimeout: number;
  private minConfidence: number;

  constructor(config: RAGConfig) {
    this.config = config;
    this.apiEndpoint = config.aiAnalysis.endpoint;
    this.requestTimeout = config.aiAnalysis.timeoutMs;
    this.minConfidence = config.aiAnalysis.minConfidence;
    this.analysisPromptTemplate = this.getDefaultAnalysisPrompt();
    
    console.log('[AIAnalyzer] 🤖 初始化AI分析器');
    console.log(`[AIAnalyzer] 🔗 API端点: ${this.apiEndpoint}`);
    console.log(`[AIAnalyzer] ⏱️ 超时设置: ${this.requestTimeout}ms`);
    console.log(`[AIAnalyzer] 🎯 最小置信度: ${this.minConfidence}`);
  }

  /**
   * Analyze user query to determine required Lynx modules
   * 分析用户查询以确定所需的Lynx模块
   */
  async analyzeQuery(query: string): Promise<AIAnalysisResult> {
    const startTime = performance.now();
    console.log(`[AIAnalyzer] 🔍 开始分析查询: "${query.substring(0, 50)}..."`);

    try {
      // Generate analysis prompt
      const analysisPrompt = this.getAnalysisPrompt(query);
      console.log(`[AIAnalyzer] 📝 分析提示词长度: ${analysisPrompt.length} 字符`);

      // Call Claude4 API for analysis
      const apiResponse = await this.callAnalysisAPI(analysisPrompt);
      console.log(`[AIAnalyzer] 📡 API响应长度: ${apiResponse.length} 字符`);

      // Parse and validate response
      const analysisResult = this.parseAnalysisResponse(apiResponse, query);
      
      // Apply confidence filtering
      const validatedResult = this.validateAnalysisResult(analysisResult, query);
      
      const processingTime = performance.now() - startTime;
      console.log(`[AIAnalyzer] ✅ 分析完成 (${processingTime.toFixed(2)}ms)`);
      console.log(`[AIAnalyzer] 📊 分析结果:`, {
        confidence: validatedResult.confidenceScore,
        complexity: validatedResult.complexity,
        intent: validatedResult.intent,
        modulesNeeded: this.countNeededModules(validatedResult),
        keywords: validatedResult.specificNeeds.length
      });

      return validatedResult;
    } catch (error) {
      console.error('[AIAnalyzer] ❌ 分析失败:', error);
      
      // Return fallback analysis
      return this.getFallbackAnalysis(query, error);
    }
  }

  /**
   * Generate analysis prompt for Claude4
   * 为Claude4生成分析提示词
   */
  getAnalysisPrompt(query: string): string {
    return this.analysisPromptTemplate.replace('{{USER_QUERY}}', query);
  }

  /**
   * Update analysis prompt template
   * 更新分析提示词模板
   */
  updatePromptTemplate(template: string): void {
    this.analysisPromptTemplate = template;
    console.log('[AIAnalyzer] 🔄 已更新分析提示词模板');
  }

  /**
   * Get default analysis prompt template
   * 获取默认分析提示词模板
   */
  private getDefaultAnalysisPrompt(): string {
    return `# Lynx框架需求分析任务

## 任务说明
分析用户的Lynx代码生成需求，确定需要哪些技术模块来完成任务。

## 用户需求
{{USER_QUERY}}

## 技术模块说明
### 1. API系统 (needsAPI)
- 包含：页面生命周期、数据管理、网络请求、本地存储
- 触发条件：需要数据获取、表单提交、API调用、状态管理
- 关键词：数据、接口、请求、存储、生命周期、API、onLoad、setData

### 2. Canvas系统 (needsCanvas)  
- 包含：Canvas 2D绘图、动画系统、交互绘图、手绘功能
- 触发条件：需要自定义图形、绘图功能、动画效果
- 关键词：绘图、图形、canvas、画布、动画、交互绘制、手绘

### 3. 图表系统 (needsCharts)
- 包含：LightChart图表库、数据可视化、25+图表类型
- 触发条件：需要数据可视化、图表展示、统计分析
- 关键词：图表、数据可视化、统计、分析、dashboard、仪表板

### 4. 高级组件 (needsAdvanced)
- 包含：复杂UI组件、高级交互、组件组合模式
- 触发条件：需要复杂界面、自定义组件、高级交互
- 关键词：复杂、高级、自定义组件、复杂交互、组合组件

### 5. 最佳实践 (needsPractices)
- 包含：性能优化、错误处理、代码质量、内存管理
- 触发条件：需要性能优化、错误处理、复杂业务逻辑
- 关键词：优化、性能、最佳实践、错误处理、代码规范

### 6. 可见性检测 (needsVisibility)
- 包含：曝光统计、可见性检测、懒加载、埋点
- 触发条件：需要统计分析、懒加载、监控功能
- 关键词：曝光、可见性、监控、埋点、懒加载

## 分析要求
1. 仔细分析用户需求的技术复杂度
2. 判断需要哪些特定的技术模块
3. 提取关键的技术词汇
4. 评估需求的复杂程度和主要意图

## 输出格式
请严格按照以下JSON格式返回分析结果：

\`\`\`json
{
  "needsAPI": boolean,
  "needsCanvas": boolean,
  "needsCharts": boolean,
  "needsAdvanced": boolean,
  "needsPractices": boolean,
  "needsVisibility": boolean,
  "specificNeeds": ["关键词1", "关键词2", "关键词3"],
  "confidenceScore": 0.95,
  "complexity": "simple|medium|complex",
  "intent": "ui_creation|data_management|visualization|interaction|optimization"
}
\`\`\`

## 分析原则
- 保守原则：不确定时倾向于不包含额外模块
- 实用原则：优先包含实际需要的模块
- 效率原则：避免包含不必要的模块以优化性能
- 准确原则：准确识别需求的核心技术要点

请开始分析：`;
  }

  /**
   * Call Claude4 API for analysis
   * 调用Claude4 API进行分析
   */
  private async callAnalysisAPI(prompt: string): Promise<string> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

    try {
      console.log('[AIAnalyzer] 🌐 调用Claude4 API进行需求分析');
      
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
          messages: [
            { role: 'user', content: prompt }
          ],
          stream: false // 不使用流式响应，获取完整结果
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API调用失败: HTTP ${response.status}`);
      }

      const responseText = await response.text();
      console.log('[AIAnalyzer] ✅ API调用成功');
      
      return responseText;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`API调用超时 (${this.requestTimeout}ms)`);
      }
      throw error;
    }
  }

  /**
   * Parse analysis response from Claude4
   * 解析Claude4的分析响应
   */
  private parseAnalysisResponse(response: string, originalQuery: string): AIAnalysisResult {
    console.log('[AIAnalyzer] 🔍 解析API响应');
    
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (!jsonMatch) {
        throw new Error('响应中未找到JSON格式的分析结果');
      }

      const jsonStr = jsonMatch[1];
      const parsed = JSON.parse(jsonStr);
      
      // Validate required fields
      const requiredFields = [
        'needsAPI', 'needsCanvas', 'needsCharts', 
        'needsAdvanced', 'needsPractices', 'needsVisibility'
      ];
      
      for (const field of requiredFields) {
        if (typeof parsed[field] !== 'boolean') {
          throw new Error(`缺少必需字段: ${field}`);
        }
      }

      // Ensure all required fields are present with defaults
      const result: AIAnalysisResult = {
        needsAPI: parsed.needsAPI || false,
        needsCanvas: parsed.needsCanvas || false,
        needsCharts: parsed.needsCharts || false,
        needsAdvanced: parsed.needsAdvanced || false,
        needsPractices: parsed.needsPractices || false,
        needsVisibility: parsed.needsVisibility || false,
        specificNeeds: Array.isArray(parsed.specificNeeds) ? parsed.specificNeeds : [],
        confidenceScore: typeof parsed.confidenceScore === 'number' ? parsed.confidenceScore : 0.5,
        complexity: ['simple', 'medium', 'complex'].includes(parsed.complexity) ? parsed.complexity : 'medium',
        intent: ['ui_creation', 'data_management', 'visualization', 'interaction', 'optimization'].includes(parsed.intent) ? parsed.intent : 'ui_creation'
      };

      console.log('[AIAnalyzer] ✅ 响应解析成功');
      return result;
    } catch (error) {
      console.error('[AIAnalyzer] ❌ 响应解析失败:', error);
      throw new Error(`响应解析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate analysis result and apply confidence filtering
   * 验证分析结果并应用置信度过滤
   */
  private validateAnalysisResult(result: AIAnalysisResult, originalQuery: string): AIAnalysisResult {
    console.log(`[AIAnalyzer] 🔍 验证分析结果 (置信度: ${result.confidenceScore})`);
    
    // Apply confidence filtering
    if (result.confidenceScore < this.minConfidence) {
      console.warn(`[AIAnalyzer] ⚠️ 置信度过低 (${result.confidenceScore} < ${this.minConfidence})，应用保守策略`);
      
      // Conservative fallback: use keyword-based heuristics
      return this.applyKeywordHeuristics(originalQuery, result);
    }

    // Additional validation logic
    result = this.applyValidationRules(result, originalQuery);
    
    console.log('[AIAnalyzer] ✅ 分析结果验证完成');
    return result;
  }

  /**
   * Apply keyword-based heuristics for low confidence cases
   * 对低置信度情况应用基于关键词的启发式方法
   */
  private applyKeywordHeuristics(query: string, originalResult: AIAnalysisResult): AIAnalysisResult {
    console.log('[AIAnalyzer] 🔍 应用关键词启发式分析');
    
    const lowerQuery = query.toLowerCase();
    const result = { ...originalResult };
    
    // API keywords
    const apiKeywords = ['数据', '接口', '请求', '存储', '用户', '登录', '提交', 'api', 'data', 'request'];
    result.needsAPI = apiKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Canvas keywords
    const canvasKeywords = ['绘图', '图形', 'canvas', '画布', '动画', '手绘', 'draw', 'graphics'];
    result.needsCanvas = canvasKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Charts keywords
    const chartKeywords = ['图表', '统计', '分析', '可视化', 'chart', 'dashboard', '仪表板'];
    result.needsCharts = chartKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Advanced keywords
    const advancedKeywords = ['复杂', '高级', '自定义', '组合', 'advanced', 'complex', 'custom'];
    result.needsAdvanced = advancedKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Practices keywords
    const practicesKeywords = ['优化', '性能', '最佳', '错误', 'optimization', 'performance', 'best'];
    result.needsPractices = practicesKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Visibility keywords
    const visibilityKeywords = ['曝光', '监控', '埋点', '懒加载', 'visibility', 'tracking', 'lazy'];
    result.needsVisibility = visibilityKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Extract specific needs
    result.specificNeeds = this.extractKeywords(query);
    
    // Set conservative confidence
    result.confidenceScore = 0.7;
    
    console.log('[AIAnalyzer] ✅ 关键词启发式分析完成');
    return result;
  }

  /**
   * Apply validation rules to analysis result
   * 对分析结果应用验证规则
   */
  private applyValidationRules(result: AIAnalysisResult, query: string): AIAnalysisResult {
    const validatedResult = { ...result };
    
    // Rule 1: If complexity is simple, limit advanced modules
    if (validatedResult.complexity === 'simple') {
      if (validatedResult.needsAdvanced && validatedResult.confidenceScore < 0.9) {
        console.log('[AIAnalyzer] 🔧 简单查询，移除高级模块');
        validatedResult.needsAdvanced = false;
      }
    }
    
    // Rule 2: If no specific technical terms, be conservative
    if (validatedResult.specificNeeds.length === 0) {
      console.log('[AIAnalyzer] 🔧 无特定技术词汇，采用保守策略');
      validatedResult.needsCanvas = false;
      validatedResult.needsVisibility = false;
    }
    
    // Rule 3: For UI creation intent, ensure basic needs are met
    if (validatedResult.intent === 'ui_creation') {
      // Basic UI usually needs some data management
      if (!validatedResult.needsAPI && query.includes('页面')) {
        console.log('[AIAnalyzer] 🔧 UI创建意图，可能需要API模块');
        validatedResult.needsAPI = true;
      }
    }
    
    return validatedResult;
  }

  /**
   * Extract keywords from query
   * 从查询中提取关键词
   */
  private extractKeywords(query: string): string[] {
    const keywords = [];
    const technicalTerms = [
      // Chinese terms
      '数据', '接口', '图表', '绘图', '动画', '优化', '性能', '监控',
      '页面', '组件', '按钮', '列表', '表单', '输入', '显示', '交互',
      // English terms
      'data', 'api', 'chart', 'canvas', 'animation', 'performance',
      'page', 'component', 'button', 'list', 'form', 'input', 'display'
    ];
    
    for (const term of technicalTerms) {
      if (query.toLowerCase().includes(term.toLowerCase())) {
        keywords.push(term);
      }
    }
    
    return keywords.slice(0, 10); // Limit to 10 keywords
  }

  /**
   * Get fallback analysis for error cases
   * 获取错误情况的降级分析
   */
  private getFallbackAnalysis(query: string, error: unknown): AIAnalysisResult {
    console.log('[AIAnalyzer] 🔄 生成降级分析结果');
    
    // Use keyword-based heuristics as fallback
    const fallbackResult: AIAnalysisResult = {
      needsAPI: false,
      needsCanvas: false,
      needsCharts: false,
      needsAdvanced: false,
      needsPractices: false,
      needsVisibility: false,
      specificNeeds: [],
      confidenceScore: 0.3,
      complexity: 'medium',
      intent: 'ui_creation'
    };
    
    return this.applyKeywordHeuristics(query, fallbackResult);
  }

  /**
   * Count needed modules in analysis result
   * 计算分析结果中需要的模块数量
   */
  private countNeededModules(result: AIAnalysisResult): number {
    const modules = [
      result.needsAPI,
      result.needsCanvas,
      result.needsCharts,
      result.needsAdvanced,
      result.needsPractices,
      result.needsVisibility
    ];
    
    return modules.filter(Boolean).length;
  }

  /**
   * Get analysis statistics
   * 获取分析统计信息
   */
  getAnalysisStatistics() {
    return {
      apiEndpoint: this.apiEndpoint,
      requestTimeout: this.requestTimeout,
      minConfidence: this.minConfidence,
      promptTemplateLength: this.analysisPromptTemplate.length
    };
  }

  /**
   * Update configuration
   * 更新配置
   */
  updateConfig(config: Partial<RAGConfig>): void {
    if (config.aiAnalysis) {
      this.apiEndpoint = config.aiAnalysis.endpoint || this.apiEndpoint;
      this.requestTimeout = config.aiAnalysis.timeoutMs || this.requestTimeout;
      this.minConfidence = config.aiAnalysis.minConfidence || this.minConfidence;
      
      if (config.aiAnalysis.promptTemplate) {
        this.analysisPromptTemplate = config.aiAnalysis.promptTemplate;
      }
    }
    
    console.log('[AIAnalyzer] 🔄 配置已更新');
  }
}