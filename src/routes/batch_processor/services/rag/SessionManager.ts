/**
 * Session Manager for Multi-turn RAG Conversations
 * 多轮RAG对话的会话管理器
 *
 * Manages conversation context, history, and preferences for
 * enhanced multi-turn dialogue support in the RAG system.
 * 为RAG系统中的增强多轮对话支持管理对话上下文、历史和偏好设置
 */

import {
  SessionContext,
  ConversationTurn,
  SessionPreferences,
  TurnPerformanceMetrics,
  AIAnalysisResult,
} from './interfaces';

/**
 * Session Manager class for handling multi-turn conversations
 * 处理多轮对话的会话管理器类
 */
export class SessionManager {
  private sessions = new Map<string, SessionContext>();
  private readonly maxSessions = 100;
  private readonly sessionTTLMs = 24 * 60 * 60 * 1000; // 24 hours
  private readonly maxTurnsPerSession = 50;

  /**
   * Create or get existing session
   * 创建或获取现有会话
   */
  createOrGetSession(sessionId?: string): SessionContext {
    // Generate session ID if not provided
    if (!sessionId) {
      sessionId = this.generateSessionId();
    }

    // Return existing session if found
    const existingSession = this.sessions.get(sessionId);
    if (existingSession) {
      // Update last activity
      existingSession.lastActivityAt = new Date();
      return existingSession;
    }

    // Create new session
    const session: SessionContext = {
      sessionId,
      createdAt: new Date(),
      lastActivityAt: new Date(),
      conversationHistory: [],
      accumulatedContext: [],
      preferences: this.getDefaultPreferences(),
      metadata: {},
    };

    this.sessions.set(sessionId, session);
    this.cleanupOldSessions();

    return session;
  }

  /**
   * Add conversation turn to session
   * 向会话添加对话轮次
   */
  addConversationTurn(
    sessionId: string,
    query: string,
    analysisResult: AIAnalysisResult,
    selectedModules: string[],
    generatedPrompt: string,
    performanceMetrics: TurnPerformanceMetrics,
  ): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`[SessionManager] ⚠️ 会话不存在: ${sessionId}`);
      return;
    }

    const turn: ConversationTurn = {
      turnId: session.conversationHistory.length + 1,
      query,
      analysisResult,
      selectedModules,
      generatedPrompt,
      timestamp: new Date(),
      performanceMetrics,
    };

    session.conversationHistory.push(turn);
    session.lastActivityAt = new Date();

    // Limit conversation history size
    if (session.conversationHistory.length > this.maxTurnsPerSession) {
      session.conversationHistory = session.conversationHistory.slice(
        -this.maxTurnsPerSession,
      );
    }

    // Update accumulated context
    this.updateAccumulatedContext(session, turn);

    console.log(
      `[SessionManager] 📝 添加对话轮次 ${turn.turnId} 到会话 ${sessionId}`,
    );
  }

  /**
   * Get conversation context for next turn
   * 获取下一轮对话的上下文
   */
  getConversationContext(sessionId: string): string {
    const session = this.sessions.get(sessionId);
    if (!session || !session.preferences.includePreviousContext) {
      return '';
    }

    const recentTurns = session.conversationHistory.slice(-3); // Last 3 turns
    const contextParts: string[] = [];

    recentTurns.forEach((turn, index) => {
      if (index < recentTurns.length - 1) {
        // Don't include current turn
        contextParts.push(`用户之前的查询: "${turn.query}"`);
        contextParts.push(`选择的模块: ${turn.selectedModules.join(', ')}`);
      }
    });

    return contextParts.length > 0
      ? `\n\n## 对话上下文\n${contextParts.join('\n')}\n\n`
      : '';
  }

  /**
   * Update session preferences
   * 更新会话偏好设置
   */
  updateSessionPreferences(
    sessionId: string,
    preferences: Partial<SessionPreferences>,
  ): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`[SessionManager] ⚠️ 会话不存在: \${sessionId}`);
      return;
    }

    session.preferences = { ...session.preferences, ...preferences };
    session.lastActivityAt = new Date();

    console.log(`[SessionManager] ⚙️ 更新会话偏好设置: ${sessionId}`);
  }

  /**
   * Get session statistics
   * 获取会话统计信息
   */
  getSessionStats(sessionId: string): any {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    const totalTurns = session.conversationHistory.length;
    const avgAnalysisTime =
      totalTurns > 0
        ? session.conversationHistory.reduce(
            (sum, turn) => sum + turn.performanceMetrics.analysisTimeMs,
            0,
          ) / totalTurns
        : 0;

    const totalTokensUsed = session.conversationHistory.reduce(
      (sum, turn) => sum + turn.performanceMetrics.totalTokensUsed,
      0,
    );

    const uniqueModules = new Set(
      session.conversationHistory.flatMap(turn => turn.selectedModules),
    ).size;

    return {
      sessionId,
      totalTurns,
      avgAnalysisTime: Math.round(avgAnalysisTime),
      totalTokensUsed,
      uniqueModules,
      sessionDuration: new Date().getTime() - session.createdAt.getTime(),
    };
  }

  /**
   * Clean up old or inactive sessions
   * 清理旧的或不活跃的会话
   */
  private cleanupOldSessions(): void {
    const now = new Date().getTime();
    const sessionsToDelete: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      const sessionAge = now - session.lastActivityAt.getTime();
      if (sessionAge > this.sessionTTLMs) {
        sessionsToDelete.push(sessionId);
      }
    }

    // Also remove oldest sessions if we exceed max count
    if (this.sessions.size > this.maxSessions) {
      const sortedSessions = Array.from(this.sessions.entries()).sort(
        ([, a], [, b]) =>
          a.lastActivityAt.getTime() - b.lastActivityAt.getTime(),
      );

      const excessCount = this.sessions.size - this.maxSessions;
      for (let i = 0; i < excessCount; i++) {
        sessionsToDelete.push(sortedSessions[i][0]);
      }
    }

    sessionsToDelete.forEach(sessionId => {
      this.sessions.delete(sessionId);
      console.log(`[SessionManager] 🗑️ 清理会话: ${sessionId}`);
    });
  }

  /**
   * Generate unique session ID
   * 生成唯一会话ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substring(2, 8);
    return `session_${timestamp}_${randomPart}`;
  }

  /**
   * Get default session preferences
   * 获取默认会话偏好设置
   */
  private getDefaultPreferences(): SessionPreferences {
    return {
      promptStyle: 'balanced',
      maxContextLength: 2000,
      includePreviousContext: true,
      moduleSelectionStrategy: 'balanced',
      customModulePriorities: {},
    };
  }

  /**
   * Update accumulated context based on conversation turn
   * 根据对话轮次更新累积上下文
   */
  private updateAccumulatedContext(
    session: SessionContext,
    turn: ConversationTurn,
  ): void {
    // Extract key information from the turn
    const contextEntry = `Q: ${turn.query} | Modules: ${turn.selectedModules.join(', ')}`;

    session.accumulatedContext.push(contextEntry);

    // Limit accumulated context size
    const maxContextEntries = 10;
    if (session.accumulatedContext.length > maxContextEntries) {
      session.accumulatedContext =
        session.accumulatedContext.slice(-maxContextEntries);
    }
  }

  /**
   * Get all active sessions (for debugging/monitoring)
   * 获取所有活跃会话（用于调试/监控）
   */
  getActiveSessions(): {
    sessionId: string;
    lastActivity: Date;
    turnCount: number;
  }[] {
    return Array.from(this.sessions.entries()).map(([sessionId, session]) => ({
      sessionId,
      lastActivity: session.lastActivityAt,
      turnCount: session.conversationHistory.length,
    }));
  }

  /**
   * Clear all sessions (for testing/reset)
   * 清除所有会话（用于测试/重置）
   */
  clearAllSessions(): void {
    this.sessions.clear();
    console.log('[SessionManager] 🧹 清除所有会话');
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();
