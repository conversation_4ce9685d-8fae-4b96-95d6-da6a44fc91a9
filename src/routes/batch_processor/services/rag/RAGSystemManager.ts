/**
 * RAG System Manager - Main Orchestrator
 * RAG系统管理器 - 主要协调器
 * 
 * Central coordinator for the AI-driven RAG system that manages
 * the entire pipeline from query analysis to prompt assembly.
 * AI驱动的RAG系统的中央协调器，管理从查询分析到提示词组装的整个流程
 */

import { 
  RAGSystemInterface, 
  RAGConfig, 
  AssembledPrompt, 
  RAGPerformanceMetrics,
  RAGError,
  RAGErrorType,
  RAGCacheEntry,
  TurnPerformanceMetrics
} from './interfaces';
import { AIAnalyzer } from './AIAnalyzer';
import { PromptAssembler } from './PromptAssembler';
import { LynxModuleRegistry } from './LynxModuleRegistry';
import { sessionManager } from './SessionManager';

/**
 * Cache manager for RAG results
 * RAG结果的缓存管理器
 */
class RAGCacheManager {
  private cache = new Map<string, RAGCacheEntry>();
  private maxEntries: number;
  private ttlMs: number;

  constructor(maxEntries: number = 100, ttlMs: number = 3600000) {
    this.maxEntries = maxEntries;
    this.ttlMs = ttlMs;
  }

  /**
   * Generate cache key from query
   * 从查询生成缓存键
   */
  private generateCacheKey(query: string): string {
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `rag_${Math.abs(hash)}`;
  }

  /**
   * Get cached result
   * 获取缓存结果
   */
  get(query: string): RAGCacheEntry | null {
    const key = this.generateCacheKey(query);
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt.getTime()) {
      this.cache.delete(key);
      return null;
    }

    // Update hit count
    entry.hitCount++;
    return entry;
  }

  /**
   * Set cache entry
   * 设置缓存条目
   */
  set(query: string, entry: Omit<RAGCacheEntry, 'key' | 'timestamp' | 'hitCount' | 'expiresAt'>): void {
    const key = this.generateCacheKey(query);
    
    // Evict oldest entries if at capacity
    if (this.cache.size >= this.maxEntries) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    const cacheEntry: RAGCacheEntry = {
      key,
      ...entry,
      timestamp: new Date(),
      hitCount: 0,
      expiresAt: new Date(Date.now() + this.ttlMs)
    };

    this.cache.set(key, cacheEntry);
  }

  /**
   * Clear all cache entries
   * 清除所有缓存条目
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * 获取缓存统计信息
   */
  getStats() {
    return {
      entries: this.cache.size,
      maxEntries: this.maxEntries,
      ttlMs: this.ttlMs,
      hitRate: this.calculateHitRate()
    };
  }

  /**
   * Calculate cache hit rate
   * 计算缓存命中率
   */
  private calculateHitRate(): number {
    const entries = Array.from(this.cache.values());
    if (entries.length === 0) return 0;
    
    const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0);
    return totalHits / entries.length;
  }
}

/**
 * Performance metrics tracker
 * 性能指标跟踪器
 */
class RAGPerformanceTracker {
  private metrics: {
    analysisTimes: number[];
    retrievalTimes: number[];
    assemblyTimes: number[];
    totalQueries: number;
    successfulQueries: number;
    cacheHits: number;
    analysisAccuracy: number[];
    tokenReductions: number[];
  };

  constructor() {
    this.metrics = {
      analysisTimes: [],
      retrievalTimes: [],
      assemblyTimes: [],
      totalQueries: 0,
      successfulQueries: 0,
      cacheHits: 0,
      analysisAccuracy: [],
      tokenReductions: []
    };
  }

  /**
   * Record analysis time
   * 记录分析时间
   */
  recordAnalysisTime(timeMs: number): void {
    this.metrics.analysisTimes.push(timeMs);
    this.keepRecentMetrics(this.metrics.analysisTimes);
  }

  /**
   * Record retrieval time
   * 记录检索时间
   */
  recordRetrievalTime(timeMs: number): void {
    this.metrics.retrievalTimes.push(timeMs);
    this.keepRecentMetrics(this.metrics.retrievalTimes);
  }

  /**
   * Record assembly time
   * 记录组装时间
   */
  recordAssemblyTime(timeMs: number): void {
    this.metrics.assemblyTimes.push(timeMs);
    this.keepRecentMetrics(this.metrics.assemblyTimes);
  }

  /**
   * Record query completion
   * 记录查询完成
   */
  recordQueryCompletion(success: boolean): void {
    this.metrics.totalQueries++;
    if (success) {
      this.metrics.successfulQueries++;
    }
  }

  /**
   * Record cache hit
   * 记录缓存命中
   */
  recordCacheHit(): void {
    this.metrics.cacheHits++;
  }

  /**
   * Record token reduction
   * 记录Token减少
   */
  recordTokenReduction(reduction: number): void {
    this.metrics.tokenReductions.push(reduction);
    this.keepRecentMetrics(this.metrics.tokenReductions);
  }

  /**
   * Keep only recent metrics to prevent memory growth
   * 只保留最近的指标以防止内存增长
   */
  private keepRecentMetrics(array: number[], maxSize: number = 100): void {
    if (array.length > maxSize) {
      array.splice(0, array.length - maxSize);
    }
  }

  /**
   * Calculate average of array
   * 计算数组平均值
   */
  private average(array: number[]): number {
    return array.length > 0 ? array.reduce((sum, val) => sum + val, 0) / array.length : 0;
  }

  /**
   * Get performance metrics
   * 获取性能指标
   */
  getMetrics(): RAGPerformanceMetrics {
    return {
      avgAnalysisTimeMs: this.average(this.metrics.analysisTimes),
      avgRetrievalTimeMs: this.average(this.metrics.retrievalTimes),
      avgAssemblyTimeMs: this.average(this.metrics.assemblyTimes),
      cacheHitRate: this.metrics.totalQueries > 0 ? 
        (this.metrics.cacheHits / this.metrics.totalQueries) * 100 : 0,
      analysisAccuracy: this.average(this.metrics.analysisAccuracy),
      tokenReduction: this.average(this.metrics.tokenReductions),
      queriesProcessed: this.metrics.totalQueries,
      successRate: this.metrics.totalQueries > 0 ? 
        (this.metrics.successfulQueries / this.metrics.totalQueries) * 100 : 0
    };
  }

  /**
   * Reset all metrics
   * 重置所有指标
   */
  reset(): void {
    this.metrics = {
      analysisTimes: [],
      retrievalTimes: [],
      assemblyTimes: [],
      totalQueries: 0,
      successfulQueries: 0,
      cacheHits: 0,
      analysisAccuracy: [],
      tokenReductions: []
    };
  }
}

/**
 * Main RAG System Manager
 * 主要RAG系统管理器
 */
export class RAGSystemManager implements RAGSystemInterface {
  private config: RAGConfig;
  private aiAnalyzer: AIAnalyzer;
  private promptAssembler: PromptAssembler;
  private moduleRegistry: LynxModuleRegistry;
  private cacheManager: RAGCacheManager;
  private performanceTracker: RAGPerformanceTracker;
  private initialized = false;
  private enabled = false;

  constructor() {
    console.log('[RAGSystemManager] 🚀 初始化RAG系统管理器');
    
    // Initialize components
    this.cacheManager = new RAGCacheManager();
    this.performanceTracker = new RAGPerformanceTracker();
    this.moduleRegistry = LynxModuleRegistry.getInstance();
  }

  /**
   * Initialize the RAG system
   * 初始化RAG系统
   */
  async initialize(config: RAGConfig): Promise<void> {
    console.log('[RAGSystemManager] 🔧 开始初始化RAG系统');
    
    try {
      this.config = config;
      this.enabled = config.enabled;

      if (!this.enabled) {
        console.log('[RAGSystemManager] ⚠️ RAG系统已禁用，将使用传统提示词加载');
        this.initialized = true;
        return;
      }

      // Initialize components
      this.aiAnalyzer = new AIAnalyzer(config);
      this.promptAssembler = new PromptAssembler(config);
      
      // Update cache configuration
      this.cacheManager = new RAGCacheManager(
        config.cache.maxEntries,
        config.cache.ttlMs
      );

      this.initialized = true;
      console.log('[RAGSystemManager] ✅ RAG系统初始化完成');
      
      // Log configuration
      this.logConfiguration();
    } catch (error) {
      console.error('[RAGSystemManager] ❌ RAG系统初始化失败:', error);
      throw new Error(`RAG系统初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Process query and return assembled prompt with session support
   * 处理查询并返回带有会话支持的组装提示词
   */
  async processQuery(query: string, sessionId?: string): Promise<AssembledPrompt> {
    console.log(`[RAGSystemManager] 🔍 处理查询: "${query.substring(0, 50)}..." ${sessionId ? `(会话: ${sessionId})` : '(无会话)'}`);
    
    if (!this.initialized) {
      throw new Error('RAG系统未初始化');
    }

    // If RAG is disabled, return fallback
    if (!this.enabled) {
      return this.createFallbackPrompt(query);
    }

    const startTime = performance.now();
    
    try {
      // 🆕 Step 0: Session management for multi-turn conversations
      const session = sessionId ? sessionManager.createOrGetSession(sessionId) : null;
      let enhancedQuery = query;
      
      if (session && session.preferences.includePreviousContext) {
        const conversationContext = sessionManager.getConversationContext(sessionId!);
        if (conversationContext) {
          enhancedQuery = `${conversationContext}当前查询: ${query}`;
          console.log(`[RAGSystemManager] 🔗 添加会话上下文 (${conversationContext.length} 字符)`);
        }
      }

      // Step 1: Check cache first (use original query for caching)
      const cacheKey = sessionId ? `${sessionId}:${query}` : query;
      const cacheEntry = this.cacheManager.get(cacheKey);
      if (cacheEntry) {
        console.log('[RAGSystemManager] 💾 缓存命中');
        this.performanceTracker.recordCacheHit();
        this.performanceTracker.recordQueryCompletion(true);
        return cacheEntry.assembledPrompt;
      }

      // Step 2: AI Analysis (use enhanced query with context)
      const analysisStartTime = performance.now();
      const analysisResult = await this.aiAnalyzer.analyzeQuery(enhancedQuery);
      const analysisTime = performance.now() - analysisStartTime;
      this.performanceTracker.recordAnalysisTime(analysisTime);

      console.log(`[RAGSystemManager] 🧠 AI分析完成 (${analysisTime.toFixed(2)}ms)`);

      // Step 3: Prompt Assembly (with session-aware assembly)
      const assemblyStartTime = performance.now();
      const contextRetrievalStartTime = performance.now();
      
      const assembledPrompt = await this.promptAssembler.assemblePrompt(
        enhancedQuery, 
        analysisResult, 
        sessionId
      );
      
      const assemblyTime = performance.now() - assemblyStartTime;
      const contextRetrievalTime = performance.now() - contextRetrievalStartTime;
      this.performanceTracker.recordAssemblyTime(assemblyTime);

      console.log(`[RAGSystemManager] 🔨 提示词组装完成 (${assemblyTime.toFixed(2)}ms)`);

      // Step 4: Calculate token reduction
      const fullPromptTokens = this.estimateFullPromptTokens();
      const actualTokens = assembledPrompt.metadata.estimatedTokens;
      const tokenReduction = ((fullPromptTokens - actualTokens) / fullPromptTokens) * 100;
      this.performanceTracker.recordTokenReduction(tokenReduction);

      // 🆕 Step 4.5: Record conversation turn in session
      if (session) {
        const turnMetrics: TurnPerformanceMetrics = {
          analysisTimeMs: analysisTime,
          contextRetrievalTimeMs: contextRetrievalTime,
          promptAssemblyTimeMs: assemblyTime,
          totalTokensUsed: assembledPrompt.metadata.estimatedTokens,
          contextTokensUsed: enhancedQuery.length - query.length, // Approximate context tokens
          newContentTokens: assembledPrompt.metadata.ragTokens
        };

        sessionManager.addConversationTurn(
          sessionId!,
          query, // Store original query, not enhanced
          analysisResult,
          assembledPrompt.metadata.includedModules,
          assembledPrompt.content,
          turnMetrics
        );

        console.log(`[RAGSystemManager] 📝 记录会话轮次到会话 ${sessionId}`);
      }

      // Step 5: Cache the result (with session-aware key)
      if (this.config.cache.enabled) {
        this.cacheManager.set(cacheKey, {
          analysisResult,
          retrievalPlan: {
            needsRAG: assembledPrompt.metadata.hasRAGContent,
            moduleNames: assembledPrompt.metadata.includedModules,
            maxChunks: assembledPrompt.metadata.includedModules.length,
            priority: analysisResult.complexity === 'complex' ? 'high' : 'medium',
            estimatedTokens: assembledPrompt.metadata.ragTokens,
            rationale: `AI分析结果 (置信度: ${analysisResult.confidenceScore})`
          },
          assembledPrompt
        });
      }

      // Step 6: Record metrics
      this.performanceTracker.recordQueryCompletion(true);
      
      const totalTime = performance.now() - startTime;
      console.log(`[RAGSystemManager] ✅ 查询处理完成 (${totalTime.toFixed(2)}ms)`);
      console.log(`[RAGSystemManager] 📊 Token减少: ${tokenReduction.toFixed(1)}%`);
      
      if (session) {
        const sessionStats = sessionManager.getSessionStats(sessionId!);
        console.log(`[RAGSystemManager] 📈 会话统计: ${sessionStats.totalTurns} 轮对话, ${sessionStats.totalTokensUsed} tokens`);
      }

      return assembledPrompt;
    } catch (error) {
      console.error('[RAGSystemManager] ❌ 查询处理失败:', error);
      
      // Record failure
      this.performanceTracker.recordQueryCompletion(false);
      
      // Apply fallback strategy
      return this.handleError(query, error);
    }
  }

  /**
   * Handle errors with fallback strategies
   * 使用降级策略处理错误
   */
  private async handleError(query: string, error: unknown): Promise<AssembledPrompt> {
    console.log('[RAGSystemManager] 🔄 应用错误处理策略');
    
    const ragError: RAGError = {
      type: this.classifyError(error),
      message: error instanceof Error ? error.message : String(error),
      originalError: error instanceof Error ? error : undefined,
      context: {
        query,
        timestamp: new Date()
      },
      fallbackApplied: false,
      fallbackStrategy: undefined
    };

    // Apply fallback based on error type and configuration
    if (this.config.fallback.enableFullPromptFallback) {
      console.log('[RAGSystemManager] 🔄 应用完整提示词降级');
      ragError.fallbackApplied = true;
      ragError.fallbackStrategy = 'full_prompt';
      
      return this.createFallbackPrompt(query);
    }

    // If no fallback is configured, rethrow the error
    throw new Error(`RAG系统错误: ${ragError.message}`);
  }

  /**
   * Classify error type
   * 分类错误类型
   */
  private classifyError(error: unknown): RAGErrorType {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      if (message.includes('timeout') || message.includes('超时')) {
        return RAGErrorType.NETWORK_ERROR;
      }
      if (message.includes('analysis') || message.includes('分析')) {
        return RAGErrorType.AI_ANALYSIS_FAILED;
      }
      if (message.includes('module') || message.includes('模块')) {
        return RAGErrorType.MODULE_RETRIEVAL_FAILED;
      }
      if (message.includes('assembly') || message.includes('组装')) {
        return RAGErrorType.PROMPT_ASSEMBLY_FAILED;
      }
      if (message.includes('token') || message.includes('预算')) {
        return RAGErrorType.TOKEN_BUDGET_EXCEEDED;
      }
      if (message.includes('cache') || message.includes('缓存')) {
        return RAGErrorType.CACHE_ERROR;
      }
    }
    
    return RAGErrorType.UNKNOWN_ERROR;
  }

  /**
   * Create fallback prompt (core modules only)
   * 创建降级提示词（仅核心模块）
   */
  private createFallbackPrompt(query: string): AssembledPrompt {
    console.log('[RAGSystemManager] 🔄 创建降级提示词');
    
    const coreModules = Object.values(this.moduleRegistry.getCoreModules());
    const coreContent = coreModules.map(module => module.content).join('\n\n');
    
    const fallbackPrompt = `# Lynx Framework Code Generation (Fallback Mode)

## User Query
"${query}"

## Core Framework Rules
${coreContent}

## Instructions
Please generate Lynx framework code based on the above core rules and user query.
`;

    return {
      corePrompt: coreContent,
      ragContent: '',
      fullPrompt: fallbackPrompt,
      metadata: {
        estimatedTokens: this.estimateTokens(fallbackPrompt),
        coreTokens: this.estimateTokens(coreContent),
        ragTokens: 0,
        hasRAGContent: false,
        includedModules: [],
        analysisResult: {
          needsAPI: false,
          needsCanvas: false,
          needsCharts: false,
          needsAdvanced: false,
          needsPractices: false,
          needsVisibility: false,
          specificNeeds: [],
          confidenceScore: 0,
          complexity: 'medium',
          intent: 'ui_creation'
        },
        assembledAt: new Date(),
        optimizationApplied: false,
        assemblyTimeMs: 0
      }
    };
  }

  /**
   * Estimate full prompt tokens (if all modules were loaded)
   * 估算完整提示词tokens（如果加载所有模块）
   */
  private estimateFullPromptTokens(): number {
    const stats = this.moduleRegistry.getStatistics();
    return stats.totalTokens;
  }

  /**
   * Estimate token count from text
   * 从文本估算Token数量
   */
  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 4); // Approximate 4 chars per token
  }

  /**
   * Log system configuration
   * 记录系统配置
   */
  private logConfiguration(): void {
    console.log('[RAGSystemManager] 📋 RAG系统配置:');
    console.log(`  🔧 启用状态: ${this.config.enabled}`);
    console.log(`  🧠 AI分析端点: ${this.config.aiAnalysis.endpoint}`);
    console.log(`  ⏱️ 分析超时: ${this.config.aiAnalysis.timeoutMs}ms`);
    console.log(`  🎯 最小置信度: ${this.config.aiAnalysis.minConfidence}`);
    console.log(`  💰 Token预算: ${this.config.tokenBudget.maxTokens}`);
    console.log(`  💾 缓存: ${this.config.cache.enabled ? '启用' : '禁用'}`);
    console.log(`  📊 监控: ${this.config.monitoring.enabled ? '启用' : '禁用'}`);
  }

  /**
   * Get current performance metrics
   * 获取当前性能指标
   */
  getMetrics(): RAGPerformanceMetrics {
    return this.performanceTracker.getMetrics();
  }

  /**
   * Clear all caches
   * 清除所有缓存
   */
  async clearCache(): Promise<void> {
    console.log('[RAGSystemManager] 🗑️ 清除缓存');
    this.cacheManager.clear();
  }

  /**
   * Update configuration
   * 更新配置
   */
  updateConfig(config: Partial<RAGConfig>): void {
    console.log('[RAGSystemManager] 🔄 更新配置');
    
    Object.assign(this.config, config);
    
    // Update components
    if (this.aiAnalyzer) {
      this.aiAnalyzer.updateConfig(this.config);
    }
    if (this.promptAssembler) {
      this.promptAssembler.updateConfig(this.config);
    }
    
    // Update enabled state
    this.enabled = this.config.enabled;
  }

  /**
   * Get system status
   * 获取系统状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      enabled: this.enabled,
      cache: this.cacheManager.getStats(),
      metrics: this.getMetrics(),
      moduleRegistry: this.moduleRegistry.getStatistics()
    };
  }

  /**
   * Dispose and cleanup resources
   * 释放和清理资源
   */
  async dispose(): Promise<void> {
    console.log('[RAGSystemManager] 🧹 清理RAG系统资源');
    
    // Clear caches
    await this.clearCache();
    
    // Reset metrics
    this.performanceTracker.reset();
    
    // Reset initialization state
    this.initialized = false;
    this.enabled = false;
    
    console.log('[RAGSystemManager] ✅ RAG系统资源清理完成');
  }
}