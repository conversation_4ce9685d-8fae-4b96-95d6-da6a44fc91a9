/**
 * RAG System Index
 * RAG系统索引
 * 
 * Central export point for all RAG system components
 * 所有RAG系统组件的中央导出点
 */

// Core interfaces and types
export * from './interfaces';

// Main system components
export { RAGSystemManager } from './RAGSystemManager';
export { AIAnalyzer } from './AIAnalyzer';
export { PromptAssembler } from './PromptAssembler';
export { LynxModuleRegistry } from './LynxModuleRegistry';

// Integration service
export { RAGIntegrationService, ragIntegrationService } from './RAGIntegrationService';

// Version information
export const RAG_SYSTEM_VERSION = '1.0.0';
export const RAG_SYSTEM_BUILD_DATE = new Date().toISOString();

/**
 * Default configuration for quick setup
 * 快速设置的默认配置
 */
export const DEFAULT_RAG_CONFIG = {
  enabled: true,
  aiAnalysis: {
    endpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
    timeoutMs: 30000,
    promptTemplate: '',
    minConfidence: 0.7
  },
  tokenBudget: {
    maxTokens: 60000,
    reservedTokens: 8000,
    coreTokenLimit: 20000,
    ragTokenLimit: 32000
  },
  cache: {
    enabled: true,
    ttlMs: 3600000,
    maxEntries: 100,
    evictionStrategy: 'lru' as const
  },
  monitoring: {
    enabled: true,
    intervalMs: 60000,
    detailedLogging: true
  },
  fallback: {
    enableAnalysisFallback: true,
    enableRetrievalFallback: true,
    enableFullPromptFallback: true,
    maxRetries: 3,
    fallbackTimeoutMs: 5000
  }
};

/**
 * Quick initialization function
 * 快速初始化函数
 */
export async function initializeRAGSystem(config?: Partial<typeof DEFAULT_RAG_CONFIG>) {
  const { ragIntegrationService } = await import('./RAGIntegrationService');
  return ragIntegrationService.initialize(config);
}

/**
 * Health check function
 * 健康检查函数
 */
export function getRAGSystemHealth() {
  const { ragIntegrationService } = require('./RAGIntegrationService');
  return ragIntegrationService.getHealthStatus();
}

/**
 * Performance metrics function
 * 性能指标函数
 */
export function getRAGSystemMetrics() {
  const { ragIntegrationService } = require('./RAGIntegrationService');
  return ragIntegrationService.getDetailedMetrics();
}

console.log(`[RAG System] 📦 RAG系统模块已加载 (v${RAG_SYSTEM_VERSION})`);
console.log(`[RAG System] 🏗️ 构建时间: ${RAG_SYSTEM_BUILD_DATE}`);
console.log(`[RAG System] 🎯 支持的功能:`);
console.log(`  🧠 AI-driven需求分析`);
console.log(`  🎯 智能模块检索`);
console.log(`  🔨 动态提示词组装`);
console.log(`  💾 多层缓存系统`);
console.log(`  📊 性能监控和指标`);
console.log(`  🔄 降级策略和错误处理`);
console.log(`  🔧 与现有系统无缝集成`);