/**
 * Prompt Assembler for RAG System
 * RAG系统的提示词组装器
 * 
 * Intelligently assembles prompts by combining core modules with
 * contextually retrieved RAG content based on AI analysis.
 * 基于AI分析智能组装提示词，将核心模块与上下文检索的RAG内容结合
 */

import { 
  AssembledPrompt, 
  PromptMetadata, 
  AIAnalysisResult, 
  LynxModule,
  RAGConfig
} from './interfaces';
import { LynxModuleRegistry } from './LynxModuleRegistry';

/**
 * Token estimation configuration
 * Token估算配置
 */
interface TokenEstimationConfig {
  /** Characters per token ratio for technical content */
  charsPerToken: number;
  /** Additional tokens for formatting and structure */
  formatTokens: number;
  /** Token overhead per module */
  moduleOverhead: number;
}

/**
 * Prompt assembly strategies
 * 提示词组装策略
 */
type AssemblyStrategy = 'conservative' | 'balanced' | 'aggressive';

/**
 * Intelligent prompt assembler that combines core and RAG content
 * 智能提示词组装器，结合核心内容和RAG内容
 */
export class PromptAssembler {
  private moduleRegistry: LynxModuleRegistry;
  private config: RAGConfig;
  private tokenEstimationConfig: TokenEstimationConfig;
  private assemblyStrategy: AssemblyStrategy;

  constructor(config: RAGConfig, strategy: AssemblyStrategy = 'balanced') {
    this.config = config;
    this.assemblyStrategy = strategy;
    this.moduleRegistry = LynxModuleRegistry.getInstance();
    
    // Configure token estimation
    this.tokenEstimationConfig = {
      charsPerToken: 4, // Approximate for technical content
      formatTokens: 200, // Overhead for formatting
      moduleOverhead: 50 // Additional tokens per module
    };

    console.log('[PromptAssembler] 🔨 初始化提示词组装器');
    console.log(`[PromptAssembler] 🎯 组装策略: ${this.assemblyStrategy}`);
    console.log(`[PromptAssembler] 📊 Token预算: ${this.config.tokenBudget.maxTokens} (核心: ${this.config.tokenBudget.coreTokenLimit}, RAG: ${this.config.tokenBudget.ragTokenLimit})`);
  }

  /**
   * Assemble prompt based on AI analysis
   * 基于AI分析组装提示词
   */
  async assemblePrompt(
    query: string, 
    analysisResult: AIAnalysisResult, 
    sessionId?: string
  ): Promise<AssembledPrompt> {
    const startTime = performance.now();
    console.log(`[PromptAssembler] 🔨 开始组装提示词: "${query.substring(0, 30)}..."`);
    console.log(`[PromptAssembler] 📊 分析结果: 置信度=${analysisResult.confidenceScore}, 复杂度=${analysisResult.complexity}`);

    try {
      // Step 1: Get core modules (always included)
      const coreModules = Object.values(this.moduleRegistry.getCoreModules());
      const coreContent = this.assembleCoreContent(coreModules);
      const coreTokens = this.estimateTokens(coreContent);

      console.log(`[PromptAssembler] 📦 核心模块: ${coreModules.length} 个, ~${coreTokens} tokens`);

      // Step 2: Get RAG modules based on analysis
      const ragModules = this.moduleRegistry.getModulesForAnalysis(analysisResult);
      const ragOnlyModules = ragModules.filter(module => !module.isCore);
      
      console.log(`[PromptAssembler] 🎯 RAG模块: ${ragOnlyModules.length} 个`);

      // Step 3: Apply token budget management
      const optimizedRAGModules = this.applyTokenBudgetOptimization(
        ragOnlyModules, 
        coreTokens, 
        analysisResult
      );

      console.log(`[PromptAssembler] ⚖️ 优化后RAG模块: ${optimizedRAGModules.length} 个`);

      // Step 4: Assemble RAG content
      const ragContent = this.assembleRAGContent(optimizedRAGModules, analysisResult);
      const ragTokens = this.estimateTokens(ragContent);

      // Step 5: Combine core and RAG content
      const fullPrompt = this.combineContent(coreContent, ragContent, query);
      const totalTokens = this.estimateTokens(fullPrompt);

      // Step 6: Apply final optimizations
      const finalPrompt = this.applyFinalOptimizations(fullPrompt, totalTokens);
      const finalTokens = this.estimateTokens(finalPrompt);

      // Step 7: Create metadata
      const metadata = this.createMetadata(
        analysisResult,
        coreTokens,
        ragTokens,
        finalTokens,
        optimizedRAGModules,
        startTime
      );

      const result: AssembledPrompt = {
        corePrompt: coreContent,
        ragContent: ragContent,
        fullPrompt: finalPrompt,
        metadata: metadata
      };

      const processingTime = performance.now() - startTime;
      console.log(`[PromptAssembler] ✅ 提示词组装完成 (${processingTime.toFixed(2)}ms)`);
      console.log(`[PromptAssembler] 📊 最终统计: ${finalTokens} tokens, ${optimizedRAGModules.length} RAG模块`);

      return result;
    } catch (error) {
      console.error('[PromptAssembler] ❌ 提示词组装失败:', error);
      
      // Return fallback prompt with core content only
      return this.createFallbackPrompt(query, analysisResult, error);
    }
  }

  /**
   * Assemble core content from core modules
   * 从核心模块组装核心内容
   */
  private assembleCoreContent(coreModules: LynxModule[]): string {
    console.log('[PromptAssembler] 📦 组装核心内容');
    
    const sections = [
      '# Lynx Framework - Core System',
      '## 🔥 CRITICAL: This is the core Lynx framework knowledge that must always be available',
      ''
    ];

    // Sort core modules by priority
    const sortedModules = coreModules.sort((a, b) => a.priority - b.priority);

    for (const module of sortedModules) {
      sections.push(`## ${module.name}`);
      sections.push(module.content);
      sections.push(''); // Empty line separator
    }

    return sections.join('\n');
  }

  /**
   * Apply token budget optimization to RAG modules
   * 对RAG模块应用Token预算优化
   */
  private applyTokenBudgetOptimization(
    ragModules: LynxModule[], 
    coreTokens: number, 
    analysisResult: AIAnalysisResult
  ): LynxModule[] {
    console.log('[PromptAssembler] ⚖️ 应用Token预算优化');
    
    const availableTokens = this.config.tokenBudget.maxTokens - 
                           coreTokens - 
                           this.config.tokenBudget.reservedTokens;
    
    console.log(`[PromptAssembler] 💰 可用Token预算: ${availableTokens}`);

    if (availableTokens <= 0) {
      console.warn('[PromptAssembler] ⚠️ Token预算不足，跳过RAG内容');
      return [];
    }

    // Apply different optimization strategies
    switch (this.assemblyStrategy) {
      case 'conservative':
        return this.applyConservativeOptimization(ragModules, availableTokens, analysisResult);
      case 'balanced':
        return this.applyBalancedOptimization(ragModules, availableTokens, analysisResult);
      case 'aggressive':
        return this.applyAggressiveOptimization(ragModules, availableTokens, analysisResult);
      default:
        return this.applyBalancedOptimization(ragModules, availableTokens, analysisResult);
    }
  }

  /**
   * Apply conservative optimization (prioritize high-confidence needs)
   * 应用保守优化（优先考虑高置信度需求）
   */
  private applyConservativeOptimization(
    ragModules: LynxModule[], 
    availableTokens: number, 
    analysisResult: AIAnalysisResult
  ): LynxModule[] {
    console.log('[PromptAssembler] 🛡️ 应用保守优化策略');
    
    // Only include modules if confidence is high
    if (analysisResult.confidenceScore < 0.8) {
      console.log('[PromptAssembler] 📉 置信度过低，跳过RAG内容');
      return [];
    }

    return this.selectModulesByTokenBudget(ragModules, availableTokens, true);
  }

  /**
   * Apply balanced optimization (balance between coverage and efficiency)
   * 应用平衡优化（在覆盖率和效率之间平衡）
   */
  private applyBalancedOptimization(
    ragModules: LynxModule[], 
    availableTokens: number, 
    analysisResult: AIAnalysisResult
  ): LynxModule[] {
    console.log('[PromptAssembler] ⚖️ 应用平衡优化策略');
    
    // Score modules based on relevance and efficiency
    const scoredModules = ragModules.map(module => ({
      module,
      score: this.calculateModuleRelevanceScore(module, analysisResult),
      efficiency: module.estimatedTokens / module.sizeKB // tokens per KB
    }));

    // Sort by score descending, then by efficiency ascending
    scoredModules.sort((a, b) => {
      if (Math.abs(a.score - b.score) > 0.1) {
        return b.score - a.score;
      }
      return a.efficiency - b.efficiency;
    });

    const selectedModules = [];
    let usedTokens = 0;

    for (const { module } of scoredModules) {
      if (usedTokens + module.estimatedTokens <= availableTokens) {
        selectedModules.push(module);
        usedTokens += module.estimatedTokens;
        console.log(`[PromptAssembler] ✅ 选择模块: ${module.name} (${module.estimatedTokens} tokens)`);
      } else {
        console.log(`[PromptAssembler] ⏭️ 跳过模块: ${module.name} (Token预算不足)`);
      }
    }

    return selectedModules;
  }

  /**
   * Apply aggressive optimization (maximize content inclusion)
   * 应用激进优化（最大化内容包含）
   */
  private applyAggressiveOptimization(
    ragModules: LynxModule[], 
    availableTokens: number, 
    analysisResult: AIAnalysisResult
  ): LynxModule[] {
    console.log('[PromptAssembler] 🚀 应用激进优化策略');
    
    // Try to include as many modules as possible, even with content trimming
    const selectedModules = this.selectModulesByTokenBudget(ragModules, availableTokens, false);
    
    // If still over budget, apply content trimming
    const totalTokens = selectedModules.reduce((sum, module) => sum + module.estimatedTokens, 0);
    
    if (totalTokens > availableTokens) {
      console.log('[PromptAssembler] ✂️ 应用内容裁剪');
      return this.trimModuleContent(selectedModules, availableTokens);
    }
    
    return selectedModules;
  }

  /**
   * Select modules by token budget
   * 根据Token预算选择模块
   */
  private selectModulesByTokenBudget(
    ragModules: LynxModule[], 
    availableTokens: number, 
    strict: boolean
  ): LynxModule[] {
    // Sort by priority (lower number = higher priority)
    const sortedModules = ragModules.sort((a, b) => a.priority - b.priority);
    
    const selectedModules = [];
    let usedTokens = 0;

    for (const module of sortedModules) {
      const moduleTokens = module.estimatedTokens + this.tokenEstimationConfig.moduleOverhead;
      
      if (usedTokens + moduleTokens <= availableTokens) {
        selectedModules.push(module);
        usedTokens += moduleTokens;
      } else if (!strict) {
        // In non-strict mode, try to include partial content
        const remainingTokens = availableTokens - usedTokens;
        if (remainingTokens > moduleTokens * 0.3) { // At least 30% of content
          selectedModules.push(module);
          break;
        }
      }
    }

    return selectedModules;
  }

  /**
   * Calculate module relevance score based on analysis
   * 根据分析计算模块相关性得分
   */
  private calculateModuleRelevanceScore(
    module: LynxModule, 
    analysisResult: AIAnalysisResult
  ): number {
    let score = 0;

    // Base score from analysis result
    switch (module.id) {
      case 'lynx_api_system':
        score = analysisResult.needsAPI ? 1.0 : 0.0;
        break;
      case 'canvas_system':
        score = analysisResult.needsCanvas ? 1.0 : 0.0;
        break;
      case 'light_chart_system':
        score = analysisResult.needsCharts ? 1.0 : 0.0;
        break;
      case 'best_practices':
        score = analysisResult.needsPractices ? 1.0 : 0.0;
        break;
      case 'visibility_detection':
        score = analysisResult.needsVisibility ? 1.0 : 0.0;
        break;
      case 'advanced_components':
        score = analysisResult.needsAdvanced ? 1.0 : 0.0;
        break;
    }

    // Boost score based on keyword matches
    const keywordMatches = module.triggerKeywords.filter(keyword =>
      analysisResult.specificNeeds.some(need => 
        need.toLowerCase().includes(keyword.toLowerCase())
      )
    );
    
    score += keywordMatches.length * 0.1;

    // Apply confidence factor
    score *= analysisResult.confidenceScore;

    return Math.min(score, 1.0);
  }

  /**
   * Trim module content to fit token budget
   * 裁剪模块内容以适应Token预算
   */
  private trimModuleContent(modules: LynxModule[], availableTokens: number): LynxModule[] {
    console.log('[PromptAssembler] ✂️ 裁剪模块内容');
    
    const totalTokens = modules.reduce((sum, module) => sum + module.estimatedTokens, 0);
    const trimRatio = availableTokens / totalTokens;

    if (trimRatio >= 1.0) {
      return modules;
    }

    // Create trimmed versions of modules
    const trimmedModules = modules.map(module => ({
      ...module,
      content: this.trimContent(module.content, trimRatio),
      estimatedTokens: Math.floor(module.estimatedTokens * trimRatio)
    }));

    return trimmedModules;
  }

  /**
   * Trim content to specified ratio
   * 按指定比例裁剪内容
   */
  private trimContent(content: string, ratio: number): string {
    if (ratio >= 1.0) {
      return content;
    }

    const lines = content.split('\n');
    const targetLength = Math.floor(lines.length * ratio);
    
    // Keep important sections (headers, examples)
    const importantLines = lines.filter(line => 
      line.trim().startsWith('#') || 
      line.trim().startsWith('```') ||
      line.trim().startsWith('*') ||
      line.trim() === ''
    );

    const regularLines = lines.filter(line => !importantLines.includes(line));
    const regularToKeep = Math.max(0, targetLength - importantLines.length);
    
    const trimmedLines = [
      ...importantLines,
      ...regularLines.slice(0, regularToKeep)
    ];

    return trimmedLines.join('\n');
  }

  /**
   * Assemble RAG content from selected modules
   * 从选定的模块组装RAG内容
   */
  private assembleRAGContent(modules: LynxModule[], analysisResult: AIAnalysisResult): string {
    if (modules.length === 0) {
      return '';
    }

    console.log('[PromptAssembler] 🎯 组装RAG内容');
    
    const sections = [
      '# Contextual Documentation (Retrieved based on your query)',
      '## 📚 The following modules were selected based on AI analysis of your requirements:',
      ''
    ];

    // Add analysis summary
    sections.push(`### Analysis Summary`);
    sections.push(`- **Query Complexity**: ${analysisResult.complexity}`);
    sections.push(`- **Primary Intent**: ${analysisResult.intent}`);
    sections.push(`- **Confidence Score**: ${analysisResult.confidenceScore.toFixed(2)}`);
    sections.push(`- **Selected Modules**: ${modules.length}`);
    sections.push('');

    // Add each selected module
    for (const module of modules) {
      sections.push(`## ${module.name}`);
      sections.push(`*Category: ${module.category} | Size: ${module.sizeKB}KB | Tokens: ~${module.estimatedTokens}*`);
      sections.push('');
      sections.push(module.content);
      sections.push('');
    }

    return sections.join('\n');
  }

  /**
   * Combine core and RAG content
   * 结合核心内容和RAG内容
   */
  private combineContent(coreContent: string, ragContent: string, query: string): string {
    const sections = [
      '# Lynx Framework Code Generation System',
      '## 🎯 User Query',
      `"${query}"`,
      '',
      coreContent
    ];

    if (ragContent.trim()) {
      sections.push('');
      sections.push('---');
      sections.push('');
      sections.push(ragContent);
    }

    sections.push('');
    sections.push('---');
    sections.push('');
    sections.push('## 📝 Instructions');
    sections.push('Please generate Lynx framework code based on the above requirements and documentation.');
    sections.push('Focus on the user query and use the provided documentation as reference.');
    sections.push('Follow all core framework rules and technical constraints.');

    return sections.join('\n');
  }

  /**
   * Apply final optimizations to the prompt
   * 对提示词应用最终优化
   */
  private applyFinalOptimizations(prompt: string, totalTokens: number): string {
    console.log(`[PromptAssembler] 🔧 应用最终优化 (当前: ${totalTokens} tokens)`);
    
    // If still over budget, apply emergency trimming
    if (totalTokens > this.config.tokenBudget.maxTokens) {
      console.warn('[PromptAssembler] ⚠️ 超出Token预算，应用紧急裁剪');
      
      const targetRatio = this.config.tokenBudget.maxTokens / totalTokens;
      return this.trimContent(prompt, targetRatio);
    }

    return prompt;
  }

  /**
   * Create prompt metadata
   * 创建提示词元数据
   */
  private createMetadata(
    analysisResult: AIAnalysisResult,
    coreTokens: number,
    ragTokens: number,
    finalTokens: number,
    includedModules: LynxModule[],
    startTime: number
  ): PromptMetadata {
    return {
      estimatedTokens: finalTokens,
      coreTokens: coreTokens,
      ragTokens: ragTokens,
      hasRAGContent: ragTokens > 0,
      includedModules: includedModules.map(m => m.name),
      analysisResult: analysisResult,
      assembledAt: new Date(),
      optimizationApplied: finalTokens < (coreTokens + ragTokens),
      assemblyTimeMs: performance.now() - startTime
    };
  }

  /**
   * Create fallback prompt for error cases
   * 为错误情况创建降级提示词
   */
  private createFallbackPrompt(
    query: string, 
    analysisResult: AIAnalysisResult, 
    error: unknown
  ): AssembledPrompt {
    console.log('[PromptAssembler] 🔄 创建降级提示词');
    
    const coreModules = Object.values(this.moduleRegistry.getCoreModules());
    const coreContent = this.assembleCoreContent(coreModules);
    const coreTokens = this.estimateTokens(coreContent);
    
    const fallbackPrompt = this.combineContent(coreContent, '', query);
    
    return {
      corePrompt: coreContent,
      ragContent: '',
      fullPrompt: fallbackPrompt,
      metadata: {
        estimatedTokens: this.estimateTokens(fallbackPrompt),
        coreTokens: coreTokens,
        ragTokens: 0,
        hasRAGContent: false,
        includedModules: [],
        analysisResult: analysisResult,
        assembledAt: new Date(),
        optimizationApplied: false,
        assemblyTimeMs: 0
      }
    };
  }

  /**
   * Estimate token count from text
   * 从文本估算Token数量
   */
  private estimateTokens(text: string): number {
    const charCount = text.length;
    const baseTokens = Math.ceil(charCount / this.tokenEstimationConfig.charsPerToken);
    
    // Add formatting overhead
    const formatTokens = this.tokenEstimationConfig.formatTokens;
    
    return baseTokens + formatTokens;
  }

  /**
   * Update configuration
   * 更新配置
   */
  updateConfig(config: Partial<RAGConfig>): void {
    Object.assign(this.config, config);
    console.log('[PromptAssembler] 🔄 配置已更新');
  }

  /**
   * Update assembly strategy
   * 更新组装策略
   */
  updateStrategy(strategy: AssemblyStrategy): void {
    this.assemblyStrategy = strategy;
    console.log(`[PromptAssembler] 🔄 组装策略已更新: ${strategy}`);
  }

  /**
   * Get current statistics
   * 获取当前统计信息
   */
  getStatistics() {
    return {
      strategy: this.assemblyStrategy,
      maxTokens: this.config.tokenBudget.maxTokens,
      coreTokenLimit: this.config.tokenBudget.coreTokenLimit,
      ragTokenLimit: this.config.tokenBudget.ragTokenLimit,
      charsPerToken: this.tokenEstimationConfig.charsPerToken,
      moduleRegistry: this.moduleRegistry.getStatistics()
    };
  }
}