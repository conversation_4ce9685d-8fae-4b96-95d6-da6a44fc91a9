/**
 * RAG Integration Service
 * RAG集成服务
 * 
 * Integrates the RAG system with existing batch processor services,
 * providing seamless enhancement to the current prompt loading system.
 * 将RAG系统与现有的批处理器服务集成，为当前提示词加载系统提供无缝增强
 */

import { RAGSystemManager } from './RAGSystemManager';
import { RAGConfig, AssembledPrompt } from './interfaces';
import { getMasterLevelLynxPromptContent } from '../../prompts/ModularPromptLoader';

/**
 * Default RAG configuration
 * 默认RAG配置
 */
const DEFAULT_RAG_CONFIG: RAGConfig = {
  enabled: true,
  aiAnalysis: {
    endpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
    timeoutMs: 30000,
    promptTemplate: '', // Will be set by AIAnalyzer
    minConfidence: 0.7
  },
  tokenBudget: {
    maxTokens: 60000,
    reservedTokens: 8000,
    coreTokenLimit: 20000,
    ragTokenLimit: 32000
  },
  cache: {
    enabled: true,
    ttlMs: 3600000, // 1 hour
    maxEntries: 100,
    evictionStrategy: 'lru'
  },
  monitoring: {
    enabled: true,
    intervalMs: 60000, // 1 minute
    detailedLogging: true
  },
  fallback: {
    enableAnalysisFallback: true,
    enableRetrievalFallback: true,
    enableFullPromptFallback: true,
    maxRetries: 3,
    fallbackTimeoutMs: 5000
  }
};

/**
 * RAG Integration Service
 * RAG集成服务
 */
export class RAGIntegrationService {
  private static instance: RAGIntegrationService;
  private ragSystemManager: RAGSystemManager;
  private initialized = false;
  private enabled = false;
  private config: RAGConfig;
  private performanceMetrics: {
    totalRequests: number;
    ragRequests: number;
    fallbackRequests: number;
    avgResponseTime: number;
    errorRate: number;
  };

  private constructor() {
    this.config = DEFAULT_RAG_CONFIG;
    this.performanceMetrics = {
      totalRequests: 0,
      ragRequests: 0,
      fallbackRequests: 0,
      avgResponseTime: 0,
      errorRate: 0
    };
    
    console.log('[RAGIntegrationService] 🔧 初始化RAG集成服务');
  }

  /**
   * Get singleton instance
   * 获取单例实例
   */
  public static getInstance(): RAGIntegrationService {
    if (!RAGIntegrationService.instance) {
      RAGIntegrationService.instance = new RAGIntegrationService();
    }
    return RAGIntegrationService.instance;
  }

  /**
   * Initialize RAG integration
   * 初始化RAG集成
   */
  async initialize(config?: Partial<RAGConfig>): Promise<void> {
    console.log('[RAGIntegrationService] 🚀 开始初始化RAG集成');
    
    try {
      // Merge configuration
      this.config = { ...DEFAULT_RAG_CONFIG, ...config };
      
      // Initialize RAG system manager
      this.ragSystemManager = new RAGSystemManager();
      await this.ragSystemManager.initialize(this.config);
      
      this.initialized = true;
      this.enabled = this.config.enabled;
      
      console.log('[RAGIntegrationService] ✅ RAG集成初始化完成');
      console.log(`[RAGIntegrationService] 🔧 RAG状态: ${this.enabled ? '启用' : '禁用'}`);
      
      // Start monitoring if enabled
      if (this.config.monitoring.enabled) {
        this.startMonitoring();
      }
    } catch (error) {
      console.error('[RAGIntegrationService] ❌ RAG集成初始化失败:', error);
      
      // Fallback to disabled state
      this.enabled = false;
      this.initialized = true;
      
      console.warn('[RAGIntegrationService] ⚠️ RAG系统已禁用，将使用传统提示词');
    }
  }

  /**
   * Enhanced prompt loading with RAG
   * 使用RAG增强的提示词加载
   */
  async getEnhancedPrompt(query: string, sessionId?: string): Promise<string> {
    const startTime = performance.now();
    this.performanceMetrics.totalRequests++;
    
    console.log(`[RAGIntegrationService] 🔍 获取增强提示词: "${query.substring(0, 50)}..."`);
    
    try {
      // Check if RAG is enabled and initialized
      if (!this.initialized || !this.enabled) {
        console.log('[RAGIntegrationService] 🔄 RAG未启用，使用传统提示词');
        return this.getFallbackPrompt(query);
      }

      // Use RAG system for enhanced prompt
      const assembledPrompt = await this.ragSystemManager.processQuery(query, sessionId);
      
      this.performanceMetrics.ragRequests++;
      
      const processingTime = performance.now() - startTime;
      this.updatePerformanceMetrics(processingTime, true);
      
      console.log(`[RAGIntegrationService] ✅ RAG增强提示词生成完成 (${processingTime.toFixed(2)}ms)`);
      console.log(`[RAGIntegrationService] 📊 提示词统计: ${assembledPrompt.metadata.estimatedTokens} tokens, ${assembledPrompt.metadata.includedModules.length} 模块`);
      
      return assembledPrompt.fullPrompt;
    } catch (error) {
      console.error('[RAGIntegrationService] ❌ RAG增强提示词生成失败:', error);
      
      // Fallback to traditional prompt
      const fallbackPrompt = this.getFallbackPrompt(query);
      
      this.performanceMetrics.fallbackRequests++;
      
      const processingTime = performance.now() - startTime;
      this.updatePerformanceMetrics(processingTime, false);
      
      console.log('[RAGIntegrationService] 🔄 已切换到传统提示词');
      
      return fallbackPrompt;
    }
  }

  /**
   * Get fallback prompt (traditional full prompt)
   * 获取降级提示词（传统完整提示词）
   */
  private getFallbackPrompt(query: string): string {
    console.log('[RAGIntegrationService] 📋 生成传统提示词');
    
    const fullPrompt = getMasterLevelLynxPromptContent();
    
    // Add query context
    const enhancedPrompt = `# Lynx Framework Code Generation

## User Query
"${query}"

## Framework Documentation
${fullPrompt}

## Instructions
Please generate Lynx framework code based on the above query and documentation.
`;
    
    return enhancedPrompt;
  }

  /**
   * Update performance metrics
   * 更新性能指标
   */
  private updatePerformanceMetrics(processingTime: number, success: boolean): void {
    // Update average response time
    this.performanceMetrics.avgResponseTime = 
      (this.performanceMetrics.avgResponseTime * (this.performanceMetrics.totalRequests - 1) + processingTime) / 
      this.performanceMetrics.totalRequests;
    
    // Update error rate
    if (!success) {
      this.performanceMetrics.errorRate = 
        (this.performanceMetrics.errorRate * (this.performanceMetrics.totalRequests - 1) + 1) / 
        this.performanceMetrics.totalRequests;
    }
  }

  /**
   * Start performance monitoring
   * 开始性能监控
   */
  private startMonitoring(): void {
    console.log('[RAGIntegrationService] 📊 启动性能监控');
    
    setInterval(() => {
      this.logPerformanceMetrics();
    }, this.config.monitoring.intervalMs);
  }

  /**
   * Log performance metrics
   * 记录性能指标
   */
  private logPerformanceMetrics(): void {
    if (this.performanceMetrics.totalRequests === 0) {
      return;
    }
    
    console.log('[RAGIntegrationService] 📊 性能指标报告:');
    console.log(`  📈 总请求数: ${this.performanceMetrics.totalRequests}`);
    console.log(`  🎯 RAG请求数: ${this.performanceMetrics.ragRequests} (${(this.performanceMetrics.ragRequests / this.performanceMetrics.totalRequests * 100).toFixed(1)}%)`);
    console.log(`  🔄 降级请求数: ${this.performanceMetrics.fallbackRequests} (${(this.performanceMetrics.fallbackRequests / this.performanceMetrics.totalRequests * 100).toFixed(1)}%)`);
    console.log(`  ⏱️ 平均响应时间: ${this.performanceMetrics.avgResponseTime.toFixed(2)}ms`);
    console.log(`  ❌ 错误率: ${(this.performanceMetrics.errorRate * 100).toFixed(1)}%`);
    
    // Get RAG system metrics
    if (this.ragSystemManager) {
      const ragMetrics = this.ragSystemManager.getMetrics();
      console.log(`  🧠 AI分析时间: ${ragMetrics.avgAnalysisTimeMs.toFixed(2)}ms`);
      console.log(`  🔨 组装时间: ${ragMetrics.avgAssemblyTimeMs.toFixed(2)}ms`);
      console.log(`  💾 缓存命中率: ${ragMetrics.cacheHitRate.toFixed(1)}%`);
      console.log(`  📉 Token减少: ${ragMetrics.tokenReduction.toFixed(1)}%`);
    }
  }

  /**
   * Get detailed metrics
   * 获取详细指标
   */
  getDetailedMetrics() {
    const ragMetrics = this.ragSystemManager ? this.ragSystemManager.getMetrics() : null;
    
    return {
      integration: {
        ...this.performanceMetrics,
        ragUtilization: this.performanceMetrics.totalRequests > 0 ? 
          (this.performanceMetrics.ragRequests / this.performanceMetrics.totalRequests * 100) : 0,
        fallbackRate: this.performanceMetrics.totalRequests > 0 ? 
          (this.performanceMetrics.fallbackRequests / this.performanceMetrics.totalRequests * 100) : 0
      },
      ragSystem: ragMetrics,
      status: {
        initialized: this.initialized,
        enabled: this.enabled,
        config: this.config
      }
    };
  }

  /**
   * Enable or disable RAG system
   * 启用或禁用RAG系统
   */
  setEnabled(enabled: boolean): void {
    console.log(`[RAGIntegrationService] 🔧 ${enabled ? '启用' : '禁用'} RAG系统`);
    
    this.enabled = enabled;
    
    if (this.ragSystemManager) {
      this.ragSystemManager.updateConfig({ enabled });
    }
  }

  /**
   * Update RAG configuration
   * 更新RAG配置
   */
  updateConfig(config: Partial<RAGConfig>): void {
    console.log('[RAGIntegrationService] 🔄 更新RAG配置');
    
    this.config = { ...this.config, ...config };
    
    if (this.ragSystemManager) {
      this.ragSystemManager.updateConfig(this.config);
    }
    
    this.enabled = this.config.enabled;
  }

  /**
   * Clear all caches
   * 清除所有缓存
   */
  async clearCache(): Promise<void> {
    console.log('[RAGIntegrationService] 🗑️ 清除RAG缓存');
    
    if (this.ragSystemManager) {
      await this.ragSystemManager.clearCache();
    }
  }

  /**
   * Test RAG system functionality
   * 测试RAG系统功能
   */
  async testRAGSystem(testQuery: string = '创建一个用户数据展示页面'): Promise<{
    success: boolean;
    responseTime: number;
    tokenCount: number;
    modulesUsed: string[];
    error?: string;
  }> {
    console.log('[RAGIntegrationService] 🧪 测试RAG系统功能');
    
    const startTime = performance.now();
    
    try {
      if (!this.initialized || !this.enabled) {
        throw new Error('RAG系统未初始化或已禁用');
      }
      
      const prompt = await this.getEnhancedPrompt(testQuery);
      const responseTime = performance.now() - startTime;
      
      // Estimate token count
      const tokenCount = Math.ceil(prompt.length / 4);
      
      // Extract modules used (simplified)
      const modulesUsed = prompt.includes('Contextual Documentation') ? 
        ['Core Modules', 'RAG Modules'] : ['Core Modules'];
      
      return {
        success: true,
        responseTime,
        tokenCount,
        modulesUsed
      };
    } catch (error) {
      const responseTime = performance.now() - startTime;
      
      return {
        success: false,
        responseTime,
        tokenCount: 0,
        modulesUsed: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get system health status
   * 获取系统健康状态
   */
  getHealthStatus() {
    return {
      initialized: this.initialized,
      enabled: this.enabled,
      healthy: this.initialized && (this.enabled ? this.performanceMetrics.errorRate < 0.1 : true),
      lastCheck: new Date(),
      issues: this.getHealthIssues()
    };
  }

  /**
   * Get health issues
   * 获取健康问题
   */
  private getHealthIssues(): string[] {
    const issues: string[] = [];
    
    if (!this.initialized) {
      issues.push('RAG系统未初始化');
    }
    
    if (this.enabled && this.performanceMetrics.errorRate > 0.1) {
      issues.push(`错误率过高: ${(this.performanceMetrics.errorRate * 100).toFixed(1)}%`);
    }
    
    if (this.enabled && this.performanceMetrics.avgResponseTime > 5000) {
      issues.push(`响应时间过长: ${this.performanceMetrics.avgResponseTime.toFixed(2)}ms`);
    }
    
    if (this.enabled && this.performanceMetrics.fallbackRequests > this.performanceMetrics.ragRequests) {
      issues.push('降级请求过多，RAG系统可能存在问题');
    }
    
    return issues;
  }

  /**
   * Reinitialize RAG system
   * 重新初始化RAG系统
   */
  async reinitialize(): Promise<void> {
    console.log('[RAGIntegrationService] 🔄 重新初始化RAG系统');
    
    // Dispose current system
    if (this.ragSystemManager) {
      await this.ragSystemManager.dispose();
    }
    
    // Reset state
    this.initialized = false;
    this.enabled = false;
    
    // Reinitialize
    await this.initialize(this.config);
  }

  /**
   * Dispose and cleanup
   * 释放和清理
   */
  async dispose(): Promise<void> {
    console.log('[RAGIntegrationService] 🧹 清理RAG集成服务');
    
    if (this.ragSystemManager) {
      await this.ragSystemManager.dispose();
    }
    
    // Reset state
    this.initialized = false;
    this.enabled = false;
    this.performanceMetrics = {
      totalRequests: 0,
      ragRequests: 0,
      fallbackRequests: 0,
      avgResponseTime: 0,
      errorRate: 0
    };
    
    console.log('[RAGIntegrationService] ✅ RAG集成服务清理完成');
  }
}

// Export singleton instance for easy access
export const ragIntegrationService = RAGIntegrationService.getInstance();