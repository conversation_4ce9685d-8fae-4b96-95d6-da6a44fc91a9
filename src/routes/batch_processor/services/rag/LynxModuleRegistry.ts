/**
 * Lynx Module Registry
 * Lynx模块注册表
 *
 * Central registry for all Lynx framework modules with metadata and content.
 * Manages core modules (always loaded) and RAG modules (context-dependent).
 * 管理所有Lynx框架模块的中央注册表，包含元数据和内容
 */

import {
  LynxModule,
  CoreModuleSet,
  RAGModuleSet,
  AIAnalysisResult,
} from './interfaces';

// Import existing prompt modules
import { getMasterLevelLynxPromptContent } from '../../prompts/ModularPromptLoader';
import { LYNX_UTILS_SYSTEM } from '../../prompts/LynxUtilsSystem';

/**
 * Central registry for all Lynx modules
 * 所有Lynx模块的中央注册表
 */
export class LynxModuleRegistry {
  private static instance: LynxModuleRegistry;
  private coreModules: CoreModuleSet;
  private ragModules: RAGModuleSet;
  private moduleMap = new Map<string, LynxModule>();
  private initialized = false;

  private constructor() {
    console.log('[LynxModuleRegistry] 🏗️ 初始化Lynx模块注册表');
    this.initializeModules();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): LynxModuleRegistry {
    if (!LynxModuleRegistry.instance) {
      LynxModuleRegistry.instance = new LynxModuleRegistry();
    }
    return LynxModuleRegistry.instance;
  }

  /**
   * Initialize all modules with metadata
   * 初始化所有模块及其元数据
   */
  private initializeModules(): void {
    console.log('[LynxModuleRegistry] 📦 开始初始化模块定义');

    // ===================================================================
    // Core Modules (Always Loaded - ~29KB)
    // 核心模块（始终加载 - 约29KB）
    // ===================================================================

    this.coreModules = {
      lynxFrameworkCore: {
        id: 'lynx_framework_core',
        name: 'Lynx Framework Core',
        content: this.extractCoreRules(),
        category: 'core',
        sizeKB: 8.2,
        estimatedTokens: 4100,
        triggerKeywords: ['output', 'format', 'FILES', 'constraint', 'rule'],
        usageScenarios: [
          'All Lynx code generation tasks',
          'Output format enforcement',
          'Critical error prevention',
        ],
        isCore: true,
        priority: 1,
        lastUpdated: new Date(),
      },

      technicalConstraints: {
        id: 'technical_constraints',
        name: 'Technical Constraints',
        content: this.extractTechnicalConstraints(),
        category: 'core',
        sizeKB: 11.9,
        estimatedTokens: 5950,
        triggerKeywords: ['CSS', 'style', 'constraint', 'limit', 'restriction'],
        usageScenarios: [
          'CSS property validation',
          'TTSS syntax enforcement',
          'Technical safety rules',
        ],
        isCore: true,
        priority: 2,
        lastUpdated: new Date(),
      },

      basicComponents: {
        id: 'basic_components',
        name: 'Basic Components',
        content: this.extractBasicComponents(),
        category: 'core',
        sizeKB: 2.9,
        estimatedTokens: 1450,
        triggerKeywords: [
          'view',
          'text',
          'image',
          'button',
          'input',
          'component',
        ],
        usageScenarios: [
          'Basic UI component usage',
          'TTML tag reference',
          'Common component patterns',
        ],
        isCore: true,
        priority: 3,
        lastUpdated: new Date(),
      },

      ttssStyleSystem: {
        id: 'ttss_style_system',
        name: 'TTSS Style System',
        content: this.extractTTSSRules(),
        category: 'core',
        sizeKB: 2.7,
        estimatedTokens: 1350,
        triggerKeywords: ['TTSS', 'style', 'CSS', 'RPX', 'unit'],
        usageScenarios: [
          'Style system usage',
          'RPX unit conversion',
          'CSS property mapping',
        ],
        isCore: true,
        priority: 4,
        lastUpdated: new Date(),
      },

      eventSystem: {
        id: 'event_system',
        name: 'Event System',
        content: this.extractEventRules(),
        category: 'core',
        sizeKB: 3.3,
        estimatedTokens: 1650,
        triggerKeywords: ['event', 'bind', 'tap', 'click', 'handler'],
        usageScenarios: [
          'Event binding and handling',
          'Touch event management',
          'Event propagation control',
        ],
        isCore: true,
        priority: 5,
        lastUpdated: new Date(),
      },
    };

    // ===================================================================
    // RAG Modules (Context-Dependent - ~70KB+)
    // RAG模块（依赖上下文 - 约70KB+）
    // ===================================================================

    this.ragModules = {
      lynxAPISystem: {
        id: 'lynx_api_system',
        name: 'Lynx API System',
        content: this.extractAPISystemRules(),
        category: 'api',
        sizeKB: 18.5,
        estimatedTokens: 9250,
        triggerKeywords: [
          '数据',
          '接口',
          '请求',
          '存储',
          '生命周期',
          'API',
          'onLoad',
          'setData',
          'request',
          'fetch',
          'storage',
          'lifecycle',
          'data',
        ],
        usageScenarios: [
          '需要数据获取和管理的页面',
          '包含表单提交的功能',
          '需要生命周期管理的复杂页面',
          'API调用和网络请求',
          '本地存储和数据持久化',
        ],
        isCore: false,
        priority: 6,
        lastUpdated: new Date(),
      },

      canvasSystem: {
        id: 'canvas_system',
        name: 'Canvas System',
        content: this.extractCanvasRules(),
        category: 'canvas',
        sizeKB: 20.3,
        estimatedTokens: 10150,
        triggerKeywords: [
          '绘图',
          '图形',
          'canvas',
          '画布',
          '动画',
          '交互绘制',
          '手绘',
          'draw',
          'graphics',
          'animation',
          'interactive',
        ],
        usageScenarios: [
          '需要自定义图形绘制',
          '交互式绘图应用',
          '动画效果实现',
          '复杂图形展示',
          '手绘功能实现',
        ],
        isCore: false,
        priority: 7,
        lastUpdated: new Date(),
      },

      lightChartSystem: {
        id: 'light_chart_system',
        name: 'LightChart System',
        content: LIGHTCHART_PROMPT_CONTENT,
        category: 'charts',
        sizeKB: 15.3,
        estimatedTokens: 7650,
        triggerKeywords: [
          '图表',
          '数据可视化',
          '统计',
          '分析',
          'dashboard',
          '仪表板',
          '柱状图',
          '折线图',
          '饼图',
          'chart',
          'visualization',
          'data',
        ],
        usageScenarios: [
          '数据分析页面',
          'Dashboard仪表板',
          '统计报表展示',
          '业务数据可视化',
          '图表组件集成',
        ],
        isCore: false,
        priority: 8,
        lastUpdated: new Date(),
      },

      bestPractices: {
        id: 'best_practices',
        name: 'Best Practices',
        content: this.extractBestPracticesRules(),
        category: 'practices',
        sizeKB: 16.3,
        estimatedTokens: 8150,
        triggerKeywords: [
          '优化',
          '性能',
          '最佳实践',
          '错误处理',
          '代码规范',
          '内存管理',
          'optimization',
          'performance',
          'best',
          'practice',
          'error',
        ],
        usageScenarios: [
          '复杂业务逻辑实现',
          '需要性能优化的场景',
          '错误处理和异常管理',
          '代码质量要求高的项目',
          '内存和性能优化',
        ],
        isCore: false,
        priority: 9,
        lastUpdated: new Date(),
      },

      visibilityDetection: {
        id: 'visibility_detection',
        name: 'Visibility Detection',
        content: this.extractVisibilityRules(),
        category: 'visibility',
        sizeKB: 8.0,
        estimatedTokens: 4000,
        triggerKeywords: [
          '曝光',
          '可见性',
          '监控',
          '埋点',
          'IntersectionObserver',
          '懒加载',
          'visibility',
          'monitor',
          'tracking',
          'lazy',
          'intersection',
        ],
        usageScenarios: [
          '需要埋点统计的页面',
          '图片懒加载实现',
          '无限滚动加载',
          '可见性监控需求',
          '用户行为分析',
        ],
        isCore: false,
        priority: 10,
        lastUpdated: new Date(),
      },

      advancedComponents: {
        id: 'advanced_components',
        name: 'Advanced Components',
        content: this.extractAdvancedComponentRules(),
        category: 'advanced',
        sizeKB: 12.0,
        estimatedTokens: 6000,
        triggerKeywords: [
          '复杂',
          '高级',
          '自定义组件',
          '复杂交互',
          '组合组件',
          'advanced',
          'complex',
          'custom',
          'composite',
          'interaction',
        ],
        usageScenarios: [
          '复杂UI组件设计',
          '高级交互效果',
          '自定义组件开发',
          '组件组合场景',
          '企业级组件库',
        ],
        isCore: false,
        priority: 11,
        lastUpdated: new Date(),
      },
    };

    // Register all modules in the map
    this.registerModulesInMap();
    this.initialized = true;

    console.log('[LynxModuleRegistry] ✅ 模块初始化完成');
    this.logModuleStats();
  }

  /**
   * Register all modules in the central map
   * 在中央映射中注册所有模块
   */
  private registerModulesInMap(): void {
    // Register core modules
    Object.values(this.coreModules).forEach(module => {
      this.moduleMap.set(module.id, module);
    });

    // Register RAG modules
    Object.values(this.ragModules).forEach(module => {
      if (module) {
        this.moduleMap.set(module.id, module);
      }
    });

    console.log(
      `[LynxModuleRegistry] 📋 已注册 ${this.moduleMap.size} 个模块到中央映射`,
    );
  }

  /**
   * Log module statistics
   * 记录模块统计信息
   */
  private logModuleStats(): void {
    const coreModuleCount = Object.keys(this.coreModules).length;
    const ragModuleCount = Object.keys(this.ragModules).filter(
      key => this.ragModules[key as keyof RAGModuleSet],
    ).length;

    const coreTotalSize = Object.values(this.coreModules).reduce(
      (sum, module) => sum + module.sizeKB,
      0,
    );
    const ragTotalSize = Object.values(this.ragModules).reduce(
      (sum, module) => sum + (module?.sizeKB || 0),
      0,
    );

    const coreTotalTokens = Object.values(this.coreModules).reduce(
      (sum, module) => sum + module.estimatedTokens,
      0,
    );
    const ragTotalTokens = Object.values(this.ragModules).reduce(
      (sum, module) => sum + (module?.estimatedTokens || 0),
      0,
    );

    console.log('[LynxModuleRegistry] 📊 模块统计信息:');
    console.log(
      `  📦 核心模块: ${coreModuleCount} 个 (${coreTotalSize}KB, ~${coreTotalTokens} tokens)`,
    );
    console.log(
      `  🎯 RAG模块: ${ragModuleCount} 个 (${ragTotalSize}KB, ~${ragTotalTokens} tokens)`,
    );
    console.log(
      `  📈 总计: ${coreModuleCount + ragModuleCount} 个模块 (${coreTotalSize + ragTotalSize}KB, ~${coreTotalTokens + ragTotalTokens} tokens)`,
    );
    console.log(
      `  💡 Token节省潜力: ${Math.round((ragTotalTokens / (coreTotalTokens + ragTotalTokens)) * 100)}% (通过智能RAG选择)`,
    );
  }

  // ===================================================================
  // Content Extraction Methods
  // 内容提取方法
  // ===================================================================

  /**
   * Extract core framework rules from existing prompt
   * 从现有提示词中提取核心框架规则
   */
  private extractCoreRules(): string {
    const fullPrompt = getMasterLevelLynxPromptContent();

    // Extract core framework rules
    return `# Lynx Framework Core Rules

## 🔥 严格输出约束 (CRITICAL OUTPUT CONSTRAINTS)

### FILES标签强制要求
- 必须使用 <FILES> 和 </FILES> 包装所有文件
- 每个文件必须用 <FILE path="文件路径"> 和 </FILE> 包装
- 禁止生成任何在FILES标签外的代码内容

### 文件路径规范
- TTML文件: src/index.ttml
- TTSS文件: src/index.ttss  
- JavaScript文件: src/index.js
- 其他资源按相对路径组织

### 致命错误防范
- 禁止生成不完整的文件结构
- 禁止遗漏必要的文件
- 禁止生成格式错误的标签结构

## 基础语法规则

### TTML基础结构
\`\`\`xml
<template>
  <view class="container">
    <!-- 内容区域 -->
  </view>
</template>
\`\`\`

### 数据绑定语法
- 文本绑定: {{variable}}
- 属性绑定: attr="{{value}}"
- 条件渲染: tt:if="{{condition}}"
- 列表渲染: tt:for="{{list}}" tt:for-item="item"

### 组件规范
- 使用语义化的组件名称
- 遵循单一职责原则
- 保持组件结构清晰简洁`;
  }

  /**
   * Extract technical constraints
   * 提取技术约束
   */
  private extractTechnicalConstraints(): string {
    return `# Technical Constraints & Safety Rules

## 🔒 强制技术约束

### CSS属性限制
**严格禁止的CSS属性：**
- position: fixed, sticky, absolute (移动端兼容性问题)
- transform: 3D transforms (性能和兼容性)
- filter: 复杂滤镜效果 (性能影响)
- backdrop-filter: 背景滤镜 (兼容性问题)

**可选链语法强制要求：**
- 所有对象属性访问必须使用 ?. 语法
- 示例: user?.name, data?.list?.[0]?.value
- 禁止直接访问可能为undefined的属性

### 滚动约束
- 禁止全局滚动控制 (body, html overflow)
- 使用scroll-view组件实现局部滚动
- 避免复杂的嵌套滚动结构

### TTSS单位系统
- 优先使用RPX响应式单位
- 1RPX = 屏幕宽度/750
- 固定值使用PX单位
- 字体大小使用适当的RPX值

### 安全规则
- 所有用户输入必须验证和转义
- 禁止直接操作DOM
- 使用框架提供的安全API`;
  }

  /**
   * Extract basic components information
   * 提取基础组件信息
   */
  private extractBasicComponents(): string {
    return `# Basic TTML Components

## 容器组件
### view - 基础容器
\`\`\`xml
<view class="container">内容</view>
\`\`\`

### scroll-view - 滚动容器
\`\`\`xml
<scroll-view scroll-y="true" class="scroll-area">
  <view>滚动内容</view>
</scroll-view>
\`\`\`

## 显示组件
### text - 文本显示
\`\`\`xml
<text class="title">{{title}}</text>
\`\`\`

### image - 图片显示
\`\`\`xml
<image src="{{imageUrl}}" class="img" mode="aspectFit" />
\`\`\`

## 表单组件
### input - 输入框
\`\`\`xml
<input placeholder="请输入" value="{{inputValue}}" bindinput="handleInput" />
\`\`\`

### button - 按钮
\`\`\`xml
<button bindtap="handleClick" class="btn">点击</button>
\`\`\`

## 媒体组件
### video - 视频播放
\`\`\`xml
<video src="{{videoUrl}}" controls class="video" />
\`\`\``;
  }

  /**
   * Extract TTSS style rules
   * 提取TTSS样式规则
   */
  private extractTTSSRules(): string {
    return `# TTSS Style System

## RPX响应式单位
- 设计稿宽度: 750RPX
- 1RPX = 屏幕宽度/750
- 自动适配不同屏幕尺寸

## 常用样式属性
### 布局属性
\`\`\`css
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 750rpx;
  height: 100vh;
}
\`\`\`

### 文本样式
\`\`\`css
.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}
\`\`\`

### 间距和尺寸
\`\`\`css
.item {
  padding: 20rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
\`\`\`

## CSS属性映射
- width/height: 支持RPX, PX, %, VH, VW
- padding/margin: 支持RPX自适应
- font-size: 建议使用RPX单位
- border-radius: 支持RPX圆角`;
  }

  /**
   * Extract event system rules
   * 提取事件系统规则
   */
  private extractEventRules(): string {
    return `# Event System & Mapping

## 触摸事件映射
### 基础点击事件
\`\`\`xml
<view bindtap="handleTap">点击我</view>
\`\`\`

### 长按事件
\`\`\`xml
<view bindlongpress="handleLongPress">长按我</view>
\`\`\`

## 表单事件
### 输入事件
\`\`\`xml
<input bindinput="handleInput" bindfocus="handleFocus" bindblur="handleBlur" />
\`\`\`

### 提交事件
\`\`\`xml
<form bindsubmit="handleSubmit">
  <button formType="submit">提交</button>
</form>
\`\`\`

## 滚动事件
\`\`\`xml
<scroll-view bindscroll="handleScroll" bindscrolltoupper="handleToUpper">
  内容
</scroll-view>
\`\`\`

## 事件传播
- bind 事件冒泡
- catch: 阻止事件冒泡
- capture-bind 事件捕获
- capture-catch: 捕获并阻止冒泡`;
  }

  /**
   * Extract API system rules (placeholder)
   * 提取API系统规则（占位符）
   */
  private extractAPISystemRules(): string {
    return `# Lynx API System & Lifecycle

## 页面生命周期
### onLoad - 页面加载
\`\`\`javascript
Page({
  onLoad(options) {
    console.log('页面加载', options);
    // 初始化数据
    this.initData();
  }
});
\`\`\`

### onShow - 页面显示
\`\`\`javascript
onShow() {
  console.log('页面显示');
  // 刷新数据
  this.refreshData();
}
\`\`\`

## 数据管理
### setData - 数据更新
\`\`\`javascript
this.setData({
  title: '新标题',
  'user.name': '新名称'
});
\`\`\`

### 网络请求
\`\`\`javascript
// HTTP请求示例
const response = await fetch('/api/data');
const data = await response.json();
this.setData({ data });
\`\`\`

## 本地存储
\`\`\`javascript
// 存储数据
localStorage.setItem('key', JSON.stringify(data));

// 读取数据
const data = JSON.parse(localStorage.getItem('key') || '{}');
\`\`\``;
  }

  /**
   * Extract canvas rules (placeholder)
   * 提取Canvas规则（占位符）
   */
  private extractCanvasRules(): string {
    return `# Canvas Drawing System

## Canvas基础使用
\`\`\`xml
<canvas canvas-id="myCanvas" class="canvas" bindtouchstart="handleTouchStart" />
\`\`\`

\`\`\`javascript


// 绘制基础图形
ctx.setFillStyle('#ff0000');
ctx.fillRect(0, 0, 100, 100);
ctx.draw();
\`\`\`

## 交互绘图
\`\`\`javascript
handleTouchStart(e) {
  const x = e.touches[0].x;
  const y = e.touches[0].y;
  
  // 开始绘制路径
  this.ctx.beginPath();
  this.ctx.moveTo(x, y);
}
\`\`\`

## 性能优化
- 使用分层渲染减少重绘
- 控制绘制频率避免卡顿
- 及时清理Canvas资源`;
  }

  /**
   * Extract best practices rules (placeholder)
   * 提取最佳实践规则（占位符）
   */
  private extractBestPracticesRules(): string {
    return `# Development Best Practices

## 性能优化
### 组件优化
- 减少不必要的setData调用
- 使用tt:if和tt:show合理控制组件显示
- 避免深层嵌套的数据结构

### 内存管理
\`\`\`javascript
// 页面卸载时清理资源
onUnload() {
  // 清理定时器
  clearInterval(this.timer);
  // 清理事件监听
  this.removeEventListeners();
}
\`\`\`

## 错误处理
\`\`\`javascript
try {
  const result = await this.apiCall();
  this.handleSuccess(result);
} catch (error) {
  console.error('API调用失败:', error);
  this.handleError(error);
}
\`\`\`

## 代码规范
- 使用有意义的变量和函数名
- 保持函数单一职责
- 添加必要的注释和文档`;
  }

  /**
   * Extract visibility detection rules (placeholder)
   * 提取可见性检测规则（占位符）
   */
  private extractVisibilityRules(): string {
    return `# Visibility Detection & Monitoring

## 页面曝光统计
\`\`\`javascript
onShow() {
  // 页面曝光统计
  this.trackPageView({
    page: 'home',
    timestamp: Date.now()
  });
}
\`\`\`

## 元素可见性检测
\`\`\`javascript
// 使用IntersectionObserver API
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      // 元素进入可视区域
      this.trackElementView(entry.target);
    }
  });
});

observer.observe(this.targetElement);
\`\`\`

## 懒加载实现
\`\`\`xml
<image 
  src="{{item.visible ? item.url : ''}}"
  class="lazy-image"
  data-src="{{item.url}}"
/>
\`\`\``;
  }

  /**
   * Extract advanced component rules (placeholder)
   * 提取高级组件规则（占位符）
   */
  private extractAdvancedComponentRules(): string {
    return `# Advanced Components & Patterns

## 自定义组件
\`\`\`xml
<!-- components/custom-card/index.ttml -->
<template>
  <view class="card">
    <view class="header">{{title}}</view>
    <view class="content">
      <slot></slot>
    </view>
  </view>
</template>
\`\`\`

## 组件通信
\`\`\`javascript
// 父子组件通信
Component({
  properties: {
    title: String,
    data: Object
  },
  
  methods: {
    handleClick() {
      this.triggerEvent('itemclick', { id: this.data.id });
    }
  }
});
\`\`\`

## 高级交互模式
- 手势识别和处理
- 复杂动画序列
- 多点触控支持
- 拖拽和缩放功能`;
  }

  // ===================================================================
  // Public API Methods
  // 公共API方法
  // ===================================================================

  /**
   * Get core modules (always loaded)
   * 获取核心模块（始终加载）
   */
  public getCoreModules(): CoreModuleSet {
    if (!this.initialized) {
      throw new Error('LynxModuleRegistry not initialized');
    }
    return this.coreModules;
  }

  /**
   * Get RAG modules (context-dependent)
   * 获取RAG模块（依赖上下文）
   */
  public getRAGModules(): RAGModuleSet {
    if (!this.initialized) {
      throw new Error('LynxModuleRegistry not initialized');
    }
    return this.ragModules;
  }

  /**
   * Get module by ID
   * 根据ID获取模块
   */
  public getModule(moduleId: string): LynxModule | undefined {
    return this.moduleMap.get(moduleId);
  }

  /**
   * Get modules based on AI analysis result
   * 根据AI分析结果获取模块
   */
  public getModulesForAnalysis(analysis: AIAnalysisResult): LynxModule[] {
    console.log('[LynxModuleRegistry] 🎯 根据AI分析结果选择模块:', analysis);

    const selectedModules: LynxModule[] = [];

    // Always include core modules
    selectedModules.push(...Object.values(this.coreModules));

    // Add RAG modules based on analysis
    if (analysis.needsAPI && this.ragModules.lynxAPISystem) {
      selectedModules.push(this.ragModules.lynxAPISystem);
      console.log('[LynxModuleRegistry] ✅ 添加API系统模块');
    }

    if (analysis.needsCanvas && this.ragModules.canvasSystem) {
      selectedModules.push(this.ragModules.canvasSystem);
      console.log('[LynxModuleRegistry] ✅ 添加Canvas系统模块');
    }

    if (analysis.needsCharts && this.ragModules.lightChartSystem) {
      selectedModules.push(this.ragModules.lightChartSystem);
      console.log('[LynxModuleRegistry] ✅ 添加图表系统模块');
    }

    if (analysis.needsPractices && this.ragModules.bestPractices) {
      selectedModules.push(this.ragModules.bestPractices);
      console.log('[LynxModuleRegistry] ✅ 添加最佳实践模块');
    }

    if (analysis.needsVisibility && this.ragModules.visibilityDetection) {
      selectedModules.push(this.ragModules.visibilityDetection);
      console.log('[LynxModuleRegistry] ✅ 添加可见性检测模块');
    }

    if (analysis.needsAdvanced && this.ragModules.advancedComponents) {
      selectedModules.push(this.ragModules.advancedComponents);
      console.log('[LynxModuleRegistry] ✅ 添加高级组件模块');
    }

    const totalTokens = selectedModules.reduce(
      (sum, module) => sum + module.estimatedTokens,
      0,
    );
    console.log(
      `[LynxModuleRegistry] 📊 选择了 ${selectedModules.length} 个模块，总计 ~${totalTokens} tokens`,
    );

    return selectedModules;
  }

  /**
   * Get all available modules
   * 获取所有可用模块
   */
  public getAllModules(): LynxModule[] {
    return Array.from(this.moduleMap.values());
  }

  /**
   * Update module content
   * 更新模块内容
   */
  public updateModule(moduleId: string, content: string): boolean {
    const module = this.moduleMap.get(moduleId);
    if (!module) {
      console.warn(`[LynxModuleRegistry] ⚠️ 模块不存在: ${moduleId}`);
      return false;
    }

    module.content = content;
    module.lastUpdated = new Date();

    console.log(`[LynxModuleRegistry] ✅ 已更新模块: ${moduleId}`);
    return true;
  }

  /**
   * Get module statistics
   * 获取模块统计信息
   */
  public getStatistics() {
    const coreModules = Object.values(this.coreModules);
    const ragModules = Object.values(this.ragModules).filter(m => m);

    return {
      totalModules: this.moduleMap.size,
      coreModuleCount: coreModules.length,
      ragModuleCount: ragModules.length,
      totalSizeKB:
        coreModules.reduce((sum, m) => sum + m.sizeKB, 0) +
        ragModules.reduce((sum, m) => sum + (m?.sizeKB || 0), 0),
      totalTokens:
        coreModules.reduce((sum, m) => sum + m.estimatedTokens, 0) +
        ragModules.reduce((sum, m) => sum + (m?.estimatedTokens || 0), 0),
      coreTokens: coreModules.reduce((sum, m) => sum + m.estimatedTokens, 0),
      ragTokens: ragModules.reduce(
        (sum, m) => sum + (m?.estimatedTokens || 0),
        0,
      ),
    };
  }

  /**
   * Search modules by keywords
   * 根据关键词搜索模块
   */
  public searchModules(keywords: string[]): LynxModule[] {
    const normalizedKeywords = keywords.map(k => k.toLowerCase());

    return Array.from(this.moduleMap.values()).filter(module => {
      return module.triggerKeywords.some(keyword =>
        normalizedKeywords.some(search =>
          keyword.toLowerCase().includes(search),
        ),
      );
    });
  }
}
