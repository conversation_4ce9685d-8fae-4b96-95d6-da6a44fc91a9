export const TIMELINE_COMPONENT = `
FILES

FILE path="index.ttml"
<view class="timeline-container">
  <view class="timeline-header">
    <text class="timeline-title">发展历程</text>
  </view>
  
  <view class="timeline-content">
    <view class="timeline-item" tt:for="{{timelineData}}" tt:key="{{item.id}}">
      <view class="timeline-date-section">
        <text class="timeline-date">{{item.date}}</text>
        <view class="timeline-dot"></view>
      </view>
      
      <view class="timeline-line" tt:if="{{!item.isLast}}"></view>
      
      <view class="timeline-content-section">
        <text class="timeline-event-title">{{item.title}}</text>
        <text class="timeline-description">{{item.description}}</text>
      </view>
    </view>
  </view>
</view>
FILE

FILE path="index.ttss"
.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #3498db;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .timeline-container {
    padding: 20rpx;
  }
  
  .timeline-title {
    font-size: 42rpx;
  }
  
  .timeline-date-section {
    min-width: 160rpx;
  }
  
  .timeline-content-section {
    padding: 20rpx;
  }
  
  .timeline-event-title {
    font-size: 28rpx;
  }
  
  .timeline-description {
    font-size: 24rpx;
  }
}
FILE

FILE path="index.js"
Card({
  data: {
    timelineData: [
      {
        id: 1,
        date: "2022年11月8日",
        title: "拜登预防性赦免",
        description: "即任前赦免福奇、米利等关键人物及全家物及全物及全",
        isLast: false
      },
      {
        id: 2,
        date: "2022年11月8日",
        title: "特朗普解雇行为",
        description: "上台台后解雇超10000名拜登政府人员",
        isLast: false
      },
      {
        id: 3,
        date: "2022年11月8日",
        title: "特朗普大规模赦免",
        description: "即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家",
        isLast: false
      },
      {
        id: 4,
        date: "2022年11月8日",
        title: "19州检察长起诉",
        description: "民主党人联合起诉特朗普行政令"违宪"",
        isLast: true
      }
    ]
  },

  onLoad() {
    console.log('Timeline component loaded');
  },

  onReady() {
    this.animateTimeline();
  },

  methods: {
    animateTimeline() {
      // 逐个显示时间线项目的动画效果
      const items = this.data.timelineData || [];
      items.forEach((item, index) => {
        setTimeout(() => {
          this.setData({
            [\`timelineData[\${index}].animated\`]: true
          });
        }, index * 200);
      });
    },

    onTimelineItemTap(event) {
      const { index } = event.currentTarget.dataset;
      const item = this.data.timelineData[index];
      
      if (item) {
        lynx.showModal({
          title: item.title,
          content: item.description,
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              console.log('用户点击确定');
            }
          }
        });
      }
    },

    onShareTimeline() {
      lynx.showShareMenu({
        title: '发展历程',
        path: '/pages/timeline/index',
        success: (res) => {
          console.log('分享成功');
        },
        fail: (err) => {
          console.error('分享失败', err);
        }
      });
    }
  }
});
FILE

FILE path="index.json"
{
  "component": true,
  "usingComponents": {},
  "styleIsolation": "isolated",
  "properties": {
    "timelineData": {
      "type": "Array",
      "value": []
    },
    "showAnimation": {
      "type": "Boolean",
      "value": true
    },
    "primaryColor": {
      "type": "String",
      "value": "#3498db"
    }
  },
  "methods": ["animateTimeline", "onTimelineItemTap", "onShareTimeline"],
  "lifetimes": ["onLoad", "onReady"],
  "observers": {
    "timelineData": function(newVal) {
      if (newVal && newVal.length > 0) {
        this.animateTimeline();
      }
    }
  }
}
FILE

FILE path="lynx.config.json"
{
  "pages": [
    "pages/timeline/index"
  ],
  "window": {
    "navigationBarTitleText": "发展历程",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#f8f9fa",
    "backgroundTextStyle": "light",
    "enablePullDownRefresh": false
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3498db",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/timeline/index",
        "text": "时间线",
        "iconPath": "images/timeline.png",
        "selectedIconPath": "images/timeline-active.png"
      }
    ]
  },
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  },
  "debug": false,
  "style": "v2",
  "sitemapLocation": "sitemap.json",
  "lazyCodeLoading": "requiredComponents",
  "renderer": "skyline",
  "componentFramework": "glass-easel",
  "rendererOptions": {
    "skyline": {
      "defaultDisplayBlock": true,
      "disableABTest": true
    }
  }
}
FILE

FILE path="configTemplate.json"
{
  "name": "timeline-component",
  "displayName": "发展历程时间线",
  "version": "1.0.0",
  "description": "基于用户提供图片设计的时间线组件，展示发展历程",
  "author": "AI Assistant",
  "homepage": "https://playground.cn.goofy.app/",
  "repository": {
    "type": "git",
    "url": "https://github.com/bytedance/lynx-components"
  },
  "keywords": ["timeline", "history", "component", "lynx", "mobile"],
  "category": "display",
  "subcategory": "timeline",
  "tags": ["时间线", "历史", "展示", "组件"],
  "preview": {
    "thumbnail": "https://lf3-static.bytedance.com/so-web-code/timeline-preview.png",
    "screenshots": [
      "https://lf3-static.bytedance.com/so-web-code/timeline-screenshot-1.png",
      "https://lf3-static.bytedance.com/so-web-code/timeline-screenshot-2.png"
    ]
  },
  "config": {
    "framework": "lynx",
    "runtime": "lynx-web",
    "sdkVersion": "3.6.0-beta.2",
    "targetPlatform": ["web", "ios", "android"],
    "dependencies": {
      "@byted-lynx/web-speedy-plugin": "^1.0.0",
      "@byted-lynx/lynx-ui": "^2.0.0"
    },
    "buildOptions": {
      "minify": true,
      "sourceMap": true,
      "compression": true
    }
  },
  "customizable": {
    "colors": {
      "primaryColor": {
        "type": "color",
        "default": "#3498db",
        "description": "主题色"
      },
      "textColor": {
        "type": "color", 
        "default": "#2c3e50",
        "description": "文字颜色"
      },
      "backgroundColor": {
        "type": "color",
        "default": "#f8f9fa", 
        "description": "背景色"
      }
    },
    "layout": {
      "showAnimation": {
        "type": "boolean",
        "default": true,
        "description": "是否显示动画效果"
      },
      "itemSpacing": {
        "type": "number",
        "default": 60,
        "min": 30,
        "max": 100,
        "description": "时间线项目间距(rpx)"
      }
    },
    "content": {
      "title": {
        "type": "string",
        "default": "发展历程",
        "description": "时间线标题"
      },
      "timelineData": {
        "type": "array",
        "default": [],
        "description": "时间线数据数组"
      }
    }
  },
  "performance": {
    "loadTime": "< 500ms",
    "renderTime": "< 100ms",
    "memoryUsage": "< 2MB",
    "bundleSize": "< 50KB"
  },
  "compatibility": {
    "lynxVersion": ">=3.6.0",
    "webView": ">=iOS 12, Android 5.0",
    "browsers": ["Chrome >=70", "Safari >=12", "Firefox >=65"]
  },
  "documentation": {
    "readme": "README.md",
    "api": "API.md",
    "examples": "examples/",
    "changelog": "CHANGELOG.md"
  },
  "license": "MIT",
  "createdAt": "2024-01-16T10:00:00Z",
  "updatedAt": "2024-01-16T10:00:00Z"
}
FILE

FILES
`;